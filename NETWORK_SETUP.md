# إعداد تطبيق الدردشة للشبكة المحلية

## نظرة عامة
تطبيق الدردشة الفورية مُعد للعمل عبر الشبكة المحلية، مما يتيح لعدة مستخدمين الوصول إليه من أجهزة مختلفة متصلة بنفس الشبكة.

## عناوين IP المتاحة
جهازك يحتوي على العناوين التالية:
- `********` - شبكة افتراضية
- `*************` - شبكة افتراضية  
- `************` - شبكة افتراضية
- `*************` - شبكة افتراضية
- `*************` - **الشبكة المحلية الرئيسية** ⭐

## كيفية الوصول للتطبيق

### من نفس الجهاز:
```
http://localhost:8000
http://127.0.0.1:8000
```

### من أجهزة أخرى في الشبكة المحلية:
```
http://*************:8000
```

## خطوات الإعداد للمستخدمين الجدد

### 1. تشغيل الخادم
```bash
cd /home/<USER>/Desktop/time-chat
python3 main.py
```

### 2. التحقق من حالة الخادم
- افتح المتصفح واذهب إلى: `http://localhost:8000/network-info`
- ستظهر لك صفحة معلومات الشبكة مع التعليمات

### 3. مشاركة الرابط
شارك الرابط التالي مع المستخدمين الآخرين:
```
http://*************:8000
```

## إنشاء حسابات المستخدمين

### المستخدم الأول (أنت):
1. اذهب إلى `http://localhost:8000`
2. انقر على تبويب "إنشاء حساب"
3. أدخل البيانات:
   - اسم المستخدم: `mazen`
   - البريد الإلكتروني: `<EMAIL>`
   - كلمة المرور: `mazen123`

### المستخدمين الآخرين:
1. اذهب إلى `http://*************:8000` من أجهزتهم
2. انقر على تبويب "إنشاء حساب"
3. أدخل بيانات مختلفة لكل مستخدم:
   - المستخدم الثاني: `ahmed` / `<EMAIL>` / `ahmed123`
   - المستخدم الثالث: `sara` / `<EMAIL>` / `sara123`

## بدء المحادثات

### محادثة فردية:
1. سجل دخولك إلى التطبيق
2. انقر على زر "+" في الشريط الجانبي
3. اختر المستخدم الذي تريد محادثته
4. ابدأ بكتابة الرسائل

### إنشاء مجموعة:
1. انقر على زر المجموعات (أيقونة المستخدمين)
2. أدخل اسم المجموعة ووصفها
3. اختر الأعضاء المراد إضافتهم
4. انقر "إنشاء المجموعة"

## الميزات المتاحة

### ✅ الميزات المكتملة:
- تسجيل المستخدمين وتسجيل الدخول
- المحادثات الفردية الفورية
- إنشاء المجموعات وإدارة الأعضاء
- إرسال الرموز التعبيرية
- رفع وإرسال الملفات (صور، مستندات، فيديو، صوت)
- البحث في الرسائل
- حالات الرسائل (مرسل، تم التسليم، تمت القراءة)
- مؤشر الكتابة
- حالة الاتصال (متصل/غير متصل)
- واجهة عربية متجاوبة وحديثة

### 🚧 قيد التطوير:
- المكالمات الصوتية والمرئية
- الرسائل الصوتية
- مشاركة الموقع
- الترجمة التلقائية
- البوتات الذكية

## استكشاف الأخطاء

### إذا لم يتمكن المستخدمون من الوصول:

1. **تحقق من الشبكة:**
   ```bash
   ping *************
   ```

2. **تحقق من الجدار الناري:**
   ```bash
   sudo ufw allow 8000
   # أو
   sudo iptables -A INPUT -p tcp --dport 8000 -j ACCEPT
   ```

3. **تحقق من حالة الخادم:**
   ```bash
   netstat -tlnp | grep :8000
   ```

4. **إعادة تشغيل الخادم:**
   ```bash
   # اضغط Ctrl+C لإيقاف الخادم ثم
   python3 main.py
   ```

### إذا كان العنوان IP مختلف:

1. **احصل على العنوان الصحيح:**
   ```bash
   hostname -I
   ip addr show
   ```

2. **حدث الملفات:**
   - عدّل `frontend/network-info.html`
   - غيّر `*************` إلى العنوان الصحيح

## نصائح للاستخدام الأمثل

### للأداء:
- استخدم شبكة Wi-Fi 5GHz للسرعة الأفضل
- تأكد من قوة الإشارة على جميع الأجهزة
- أغلق التطبيقات غير المستخدمة

### للأمان:
- استخدم كلمات مرور قوية
- لا تشارك الرابط خارج الشبكة المحلية
- راقب المستخدمين المتصلين

### للتجربة:
- جرب جميع الميزات المتاحة
- أرسل أنواع مختلفة من الملفات
- اختبر المحادثات الجماعية
- استخدم البحث في الرسائل

## معلومات تقنية

### المتطلبات:
- Python 3.8+
- FastAPI
- SQLite
- WebSocket support

### المنافذ المستخدمة:
- `8000` - HTTP/WebSocket

### قاعدة البيانات:
- SQLite: `chat.db`
- النسخ الاحتياطية في: `logs/`

### الملفات المرفوعة:
- مجلد: `uploads/`
- الحد الأقصى: 50MB لكل ملف

## الدعم والمساعدة

إذا واجهت أي مشاكل:
1. تحقق من ملف السجلات: `logs/app.log`
2. راجع هذا الدليل
3. أعد تشغيل الخادم
4. تواصل مع مدير النظام

---

**ملاحظة:** هذا التطبيق مُصمم للاستخدام في الشبكة المحلية فقط. لا تعرضه للإنترنت العام بدون إعدادات أمان إضافية.
