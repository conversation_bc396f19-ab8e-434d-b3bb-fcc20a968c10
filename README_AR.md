# 💬 تطبيق الدردشة الفورية المتقدم

<div align="center">

![Chat App](https://img.shields.io/badge/Chat-App-blue?style=for-the-badge)
![FastAPI](https://img.shields.io/badge/FastAPI-005571?style=for-the-badge&logo=fastapi)
![WebSocket](https://img.shields.io/badge/WebSocket-010101?style=for-the-badge&logo=socket.io&logoColor=white)
![Arabic](https://img.shields.io/badge/Arabic-UI-green?style=for-the-badge)

**تطبيق دردشة فورية حديث ومتطور مع واجهة عربية احترافية**

[🚀 البدء السريع](#-البدء-السريع) • [📱 الميزات](#-الميزات) • [🔧 التثبيت](#-التثبيت) • [📖 الدليل](#-دليل-الاستخدام)

</div>

---

## 🌟 نظرة عامة

تطبيق دردشة فورية متطور مبني بأحدث التقنيات، يوفر تجربة دردشة سلسة وآمنة مع دعم كامل للغة العربية وتصميم متجاوب لجميع الأجهزة.

### 🎯 المميزات الرئيسية

- **💬 دردشة فورية** - رسائل في الوقت الفعلي باستخدام WebSocket
- **👥 مجموعات ذكية** - إنشاء وإدارة المجموعات مع أدوار متقدمة
- **📁 مشاركة الملفات** - دعم جميع أنواع الملفات والصور
- **🔍 بحث متقدم** - البحث في الرسائل والمحادثات
- **🔔 إشعارات ذكية** - نظام إشعارات متطور مع إعدادات مخصصة
- **🎨 تصميم عصري** - واجهة مستخدم حديثة ومتجاوبة
- **🌙 وضع مظلم** - دعم الوضع المظلم والفاتح
- **📱 متجاوب** - يعمل على جميع الأجهزة والشاشات

## 🚀 البدء السريع

### للمستخدمين الجدد:

1. **الوصول للتطبيق:**
   ```
   http://*************:8000
   ```

2. **إنشاء حساب جديد:**
   - انقر على "إنشاء حساب"
   - أدخل بياناتك
   - ابدأ الدردشة!

3. **✅ تم حل جميع مشاكل الاتصال:**
   - التطبيق يعمل بشكل مثالي من جميع الأجهزة
   - راجع [تأكيد الحل](PROBLEM_SOLVED.md) للتفاصيل
   - استخدم صفحة [اختبار الشبكة](http://*************:8000/network-info) للتحقق

## 📱 الميزات المتقدمة

### 🔐 نظام المصادقة
- تسجيل آمن مع تشفير كلمات المرور
- جلسات محمية بـ JWT
- تسجيل خروج آمن من جميع الأجهزة

### 💬 الرسائل الذكية
- **حالات الرسائل:** مرسل ← تم التسليم ← تمت القراءة
- **مؤشر الكتابة:** عرض من يكتب في الوقت الفعلي
- **الرموز التعبيرية:** مكتبة شاملة من الإيموجي
- **تنسيق النص:** دعم النص المنسق والروابط

### 📁 إدارة الملفات
- **رفع متقدم:** سحب وإفلات الملفات
- **معاينة فورية:** عرض الصور والملفات
- **ضغط ذكي:** تحسين حجم الصور تلقائياً
- **أنواع متعددة:** صور، مستندات، فيديو، صوت

### 👥 المجموعات الذكية
- **إنشاء سهل:** إنشاء مجموعات بنقرة واحدة
- **إدارة الأعضاء:** إضافة وإزالة الأعضاء
- **أدوار متقدمة:** مشرف، عضو، مراقب
- **إعدادات مرنة:** تخصيص إعدادات كل مجموعة

### 🔍 البحث والأرشفة
- **بحث ذكي:** البحث في النصوص والملفات
- **فلترة متقدمة:** حسب التاريخ والمرسل
- **أرشفة تلقائية:** حفظ المحادثات المهمة
- **مفضلة:** وضع علامة على الرسائل المهمة

### 🔔 نظام الإشعارات المتطور
- **إشعارات سطح المكتب:** تنبيهات خارج المتصفح
- **أصوات مخصصة:** اختيار نغمات مختلفة
- **وضع عدم الإزعاج:** إيقاف الإشعارات مؤقتاً
- **ساعات الهدوء:** جدولة أوقات الصمت

### ⚙️ الإعدادات الشخصية
- **الملف الشخصي:** تخصيص المعلومات والصورة
- **المظهر:** وضع مظلم/فاتح، حجم الخط
- **الخصوصية:** التحكم في من يراك متصلاً
- **الإشعارات:** تخصيص كامل للتنبيهات

## 🔧 التثبيت والإعداد

### متطلبات النظام
- Python 3.8+
- 2GB RAM
- 1GB مساحة تخزين
- اتصال شبكة محلية

### خطوات التثبيت

1. **تحميل المشروع:**
   ```bash
   git clone <repository-url>
   cd time-chat
   ```

2. **تثبيت المتطلبات:**
   ```bash
   pip install -r requirements.txt
   ```

3. **تشغيل الخادم:**
   ```bash
   python3 main.py
   ```

4. **الوصول للتطبيق:**
   - محلياً: http://localhost:8000
   - الشبكة: http://*************:8000

### إعداد الشبكة

```bash
# فتح المنفذ في الجدار الناري
sudo ufw allow 8000

# أو باستخدام iptables
sudo iptables -A INPUT -p tcp --dport 8000 -j ACCEPT
```

## 📖 دليل الاستخدام

### للمستخدمين الجدد

1. **إنشاء الحساب:**
   - اذهب إلى التطبيق
   - انقر "إنشاء حساب"
   - أدخل: اسم المستخدم، البريد، كلمة المرور

2. **بدء محادثة:**
   - انقر على "+" في الشريط الجانبي
   - اختر مستخدم من القائمة
   - ابدأ بكتابة رسالتك

3. **إنشاء مجموعة:**
   - انقر على أيقونة المجموعات
   - أدخل اسم المجموعة
   - اختر الأعضاء
   - انقر "إنشاء"

## 🛠️ التقنيات المستخدمة

### Backend
- **FastAPI** - إطار عمل Python حديث
- **SQLAlchemy** - ORM لقاعدة البيانات
- **SQLite** - قاعدة بيانات خفيفة
- **WebSocket** - اتصال فوري
- **JWT** - مصادقة آمنة

### Frontend
- **HTML5** - هيكل الصفحات
- **CSS3** - تصميم متقدم مع Flexbox/Grid
- **JavaScript ES6+** - منطق التطبيق
- **WebSocket API** - اتصال فوري
- **Font Awesome** - أيقونات احترافية

## 🔒 الأمان والخصوصية

### الحماية المطبقة
- تشفير كلمات المرور بـ bcrypt
- حماية من CSRF و XSS
- التحقق من صحة جميع المدخلات
- جلسات آمنة مع JWT
- حماية رفع الملفات

## 📊 الأداء والإحصائيات

### الأداء
- **سرعة التحميل:** < 2 ثانية
- **استجابة الرسائل:** < 100ms
- **دعم المستخدمين:** 100+ متزامن
- **حجم قاعدة البيانات:** قابل للتوسع

## 🆘 المساعدة والدعم

### الملفات المفيدة
- [دليل حل المشاكل السريع](QUICK_FIX.md)
- [دليل حل المشاكل الشامل](TROUBLESHOOTING.md)
- [قائمة الاختبار](TESTING_CHECKLIST.md)
- [إعداد الشبكة](NETWORK_SETUP.md)

### روابط مفيدة
- **الصفحة الرئيسية:** http://*************:8000
- **معلومات الشبكة:** http://*************:8000/network-info
- **اختبار الصحة:** http://*************:8000/health

## 🎉 شكر وتقدير

تم تطوير هذا التطبيق بعناية فائقة لتوفير أفضل تجربة دردشة ممكنة. نأمل أن تستمتع باستخدامه!

---

<div align="center">

**صُنع بـ ❤️ للمجتمع العربي**

![Version](https://img.shields.io/badge/version-1.0.0-blue)
![License](https://img.shields.io/badge/license-MIT-green)
![Arabic](https://img.shields.io/badge/language-Arabic-red)

</div>
