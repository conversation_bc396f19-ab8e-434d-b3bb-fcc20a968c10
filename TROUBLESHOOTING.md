# دليل حل مشاكل الاتصال - تطبيق الدردشة

## المشكلة: "يرجى التحقق من الإنترنت"

إذا ظهرت هذه الرسالة عند محاولة الوصول للتطبيق من جهاز آخر، اتبع الخطوات التالية:

## 1. التحقق من الشبكة

### تأكد من الاتصال بنفس الشبكة:
```bash
# على جهاز الخادم (جهاز مازن)
ip addr show | grep 192.168

# على الجهاز الآخر
ping *************
```

### إذا فشل الـ ping:
- تأكد من أن كلا الجهازين متصلان بنفس الـ Wi-Fi
- تحقق من إعدادات الراوتر (قد يكون هناك عزل بين الأجهزة)

## 2. التحقق من الخادم

### على جهاز الخادم، تحقق من أن الخادم يعمل:
```bash
# التحقق من أن الخادم يستمع على المنفذ 8000
netstat -tlnp | grep :8000

# يجب أن ترى شيئاً مثل:
# tcp 0 0 0.0.0.0:8000 0.0.0.0:* LISTEN
```

### إذا لم يكن الخادم يعمل:
```bash
cd /home/<USER>/Desktop/time-chat
python3 main.py
```

## 3. فتح المنفذ في الجدار الناري

### Ubuntu/Debian:
```bash
sudo ufw allow 8000
sudo ufw reload
```

### أو باستخدام iptables:
```bash
sudo iptables -A INPUT -p tcp --dport 8000 -j ACCEPT
sudo iptables-save
```

### CentOS/RHEL:
```bash
sudo firewall-cmd --permanent --add-port=8000/tcp
sudo firewall-cmd --reload
```

## 4. اختبار الاتصال

### من الجهاز الآخر:
```bash
# اختبار الاتصال الأساسي
telnet ************* 8000

# أو باستخدام curl
curl http://*************:8000
```

### من المتصفح:
1. افتح المتصفح
2. اذهب إلى: `http://*************:8000`
3. يجب أن تظهر صفحة التطبيق

## 5. حلول للمشاكل الشائعة

### المشكلة: "Connection refused"
**الحل:**
- تأكد من أن الخادم يعمل
- تحقق من أن المنفذ 8000 مفتوح
- أعد تشغيل الخادم

### المشكلة: "Network unreachable"
**الحل:**
- تحقق من اتصال الشبكة
- تأكد من أن كلا الجهازين على نفس الشبكة
- جرب عنوان IP مختلف

### المشكلة: "Timeout"
**الحل:**
- تحقق من إعدادات الجدار الناري
- تأكد من أن الراوتر لا يحجب الاتصالات
- جرب إيقاف الجدار الناري مؤقتاً للاختبار

## 6. إعدادات الراوتر

إذا كانت المشكلة مستمرة، قد تحتاج لتغيير إعدادات الراوتر:

### إيقاف AP Isolation:
1. ادخل إلى إعدادات الراوتر (عادة *********** أو ***********)
2. ابحث عن "AP Isolation" أو "Client Isolation"
3. تأكد من أنه مُعطل (Disabled)

### فتح المنفذ في الراوتر:
1. اذهب إلى Port Forwarding
2. أضف قاعدة جديدة:
   - Port: 8000
   - Protocol: TCP
   - Internal IP: *************

## 7. اختبار سريع

### على جهاز الخادم:
```bash
# تشغيل خادم اختبار بسيط
python3 -m http.server 8001

# من جهاز آخر، جرب الوصول إلى:
# http://*************:8001
```

إذا عمل هذا، فالمشكلة في تطبيق الدردشة وليس في الشبكة.

## 8. معلومات الشبكة الحالية

### عناوين IP المتاحة:
- `*************` - الشبكة المحلية الرئيسية ⭐
- `localhost` / `127.0.0.1` - للوصول المحلي فقط

### المنافذ المستخدمة:
- `8000` - تطبيق الدردشة (HTTP + WebSocket)

## 9. خطوات التشخيص السريع

### للمستخدم الذي يواجه المشكلة:

1. **اختبر الشبكة:**
   ```
   ping *************
   ```

2. **اختبر المنفذ:**
   ```
   telnet ************* 8000
   ```

3. **جرب المتصفح:**
   - اذهب إلى: `http://*************:8000`

4. **إذا لم يعمل، جرب:**
   - أعد تشغيل الـ Wi-Fi
   - جرب متصفح مختلف
   - امسح cache المتصفح

### لمدير الخادم (مازن):

1. **تحقق من الخادم:**
   ```bash
   netstat -tlnp | grep :8000
   ```

2. **تحقق من الجدار الناري:**
   ```bash
   sudo ufw status
   sudo ufw allow 8000
   ```

3. **أعد تشغيل الخادم:**
   ```bash
   cd /home/<USER>/Desktop/time-chat
   python3 main.py
   ```

## 10. معلومات الاتصال للدعم

إذا استمرت المشكلة:
1. تحقق من ملف السجلات: `logs/app.log`
2. شارك رسالة الخطأ الكاملة
3. أرسل نتيجة الأوامر التشخيصية

---

**ملاحظة:** هذا التطبيق مُصمم للشبكة المحلية فقط. تأكد من أن جميع المستخدمين متصلون بنفس الشبكة.
