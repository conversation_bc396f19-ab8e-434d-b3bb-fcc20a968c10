<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>معلومات الشبكة - تطبيق الدردشة</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            max-width: 600px;
            width: 100%;
            text-align: center;
        }

        .header {
            margin-bottom: 30px;
        }

        .header h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 2rem;
        }

        .header p {
            color: #666;
            font-size: 1.1rem;
        }

        .network-info {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
        }

        .ip-address {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            font-size: 1.5rem;
            font-weight: bold;
            letter-spacing: 1px;
        }

        .instructions {
            text-align: right;
            margin: 30px 0;
        }

        .instructions h3 {
            color: #333;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .instructions ol {
            color: #666;
            line-height: 1.8;
            padding-right: 20px;
        }

        .instructions li {
            margin-bottom: 10px;
        }

        .qr-code {
            background: white;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            display: inline-block;
        }

        .status {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            margin: 20px 0;
            padding: 15px;
            background: #d4edda;
            color: #155724;
            border-radius: 10px;
            border: 1px solid #c3e6cb;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            margin: 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
        }

        .warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
            border-radius: 10px;
            padding: 15px;
            margin: 20px 0;
            text-align: right;
        }

        .device-list {
            background: #e3f2fd;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            text-align: right;
        }

        .device-list h4 {
            color: #1976d2;
            margin-bottom: 15px;
        }

        .device-list ul {
            list-style: none;
            padding: 0;
        }

        .device-list li {
            background: white;
            margin: 10px 0;
            padding: 10px 15px;
            border-radius: 8px;
            border-right: 4px solid #1976d2;
        }

        @media (max-width: 768px) {
            .container {
                padding: 20px;
            }
            
            .header h1 {
                font-size: 1.5rem;
            }
            
            .ip-address {
                font-size: 1.2rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-network-wired"></i> معلومات الشبكة</h1>
            <p>تطبيق الدردشة الفورية متاح الآن عبر الشبكة المحلية</p>
        </div>

        <div class="status">
            <i class="fas fa-check-circle"></i>
            <span>الخادم يعمل بنجاح على المنفذ 8000</span>
        </div>

        <div class="network-info">
            <h3><i class="fas fa-globe"></i> عنوان IP الخاص بالخادم:</h3>
            <div class="ip-address" id="serverIP">
                *************:8000
            </div>
        </div>

        <div class="instructions">
            <h3><i class="fas fa-mobile-alt"></i> كيفية الوصول من الأجهزة الأخرى:</h3>
            <ol>
                <li>تأكد من أن الجهاز متصل بنفس الشبكة المحلية (Wi-Fi)</li>
                <li>افتح متصفح الويب على الجهاز الآخر</li>
                <li>اكتب العنوان التالي في شريط العناوين:</li>
                <div class="ip-address">http://*************:8000</div>
                <li>اضغط Enter للوصول إلى التطبيق</li>
                <li>قم بإنشاء حساب جديد أو تسجيل الدخول</li>
            </ol>
        </div>

        <div class="device-list">
            <h4><i class="fas fa-devices"></i> الأجهزة المدعومة:</h4>
            <ul>
                <li><i class="fas fa-laptop"></i> أجهزة الكمبيوتر المحمولة</li>
                <li><i class="fas fa-desktop"></i> أجهزة الكمبيوتر المكتبية</li>
                <li><i class="fas fa-tablet-alt"></i> الأجهزة اللوحية</li>
                <li><i class="fas fa-mobile-alt"></i> الهواتف الذكية</li>
            </ul>
        </div>

        <div class="warning">
            <i class="fas fa-exclamation-triangle"></i>
            <strong>ملاحظة مهمة:</strong> تأكد من أن جميع الأجهزة متصلة بنفس الشبكة المحلية (نفس الـ Wi-Fi أو الشبكة السلكية).
        </div>

        <div style="margin-top: 30px;">
            <a href="/" class="btn">
                <i class="fas fa-comments"></i> الذهاب إلى التطبيق
            </a>
            <button class="btn" onclick="copyToClipboard()">
                <i class="fas fa-copy"></i> نسخ الرابط
            </button>
            <button class="btn" onclick="testConnection()" style="background: #28a745;">
                <i class="fas fa-wifi"></i> اختبار الاتصال
            </button>
        </div>

        <!-- نتائج اختبار الاتصال -->
        <div id="connectionTest" style="display: none; margin-top: 20px; padding: 20px; background: #f8f9fa; border-radius: 10px;">
            <h4><i class="fas fa-cogs"></i> نتائج اختبار الاتصال:</h4>
            <div id="testResults"></div>
        </div>

        <div style="margin-top: 20px; color: #666; font-size: 0.9rem;">
            <p>للحصول على المساعدة، تواصل مع مدير النظام</p>
        </div>
    </div>

    <script>
        // نسخ الرابط إلى الحافظة
        function copyToClipboard() {
            const url = 'http://*************:8000';
            navigator.clipboard.writeText(url).then(function() {
                alert('تم نسخ الرابط إلى الحافظة!');
            }, function(err) {
                console.error('فشل في نسخ الرابط: ', err);
                // طريقة بديلة للنسخ
                const textArea = document.createElement('textarea');
                textArea.value = url;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                alert('تم نسخ الرابط إلى الحافظة!');
            });
        }

        // تحديث عنوان IP تلقائياً
        function updateIPAddress() {
            const serverIP = document.getElementById('serverIP');
            const currentHost = window.location.hostname;
            const currentPort = window.location.port || '8000';
            
            if (currentHost !== 'localhost' && currentHost !== '127.0.0.1') {
                serverIP.textContent = `${currentHost}:${currentPort}`;
                
                // تحديث جميع عناوين IP في الصفحة
                const ipElements = document.querySelectorAll('.ip-address');
                ipElements.forEach(element => {
                    if (element.textContent.includes('*************')) {
                        element.textContent = element.textContent.replace('*************', currentHost);
                    }
                });
            }
        }

        // تشغيل التحديث عند تحميل الصفحة
        window.addEventListener('load', updateIPAddress);

        // عرض معلومات إضافية عن الشبكة
        function showNetworkInfo() {
            console.log('Network Information:');
            console.log('Host:', window.location.hostname);
            console.log('Port:', window.location.port);
            console.log('Protocol:', window.location.protocol);
            console.log('Full URL:', window.location.href);
        }

        showNetworkInfo();

        // اختبار الاتصال
        async function testConnection() {
            const testDiv = document.getElementById('connectionTest');
            const resultsDiv = document.getElementById('testResults');

            testDiv.style.display = 'block';
            resultsDiv.innerHTML = '<p><i class="fas fa-spinner fa-spin"></i> جاري اختبار الاتصال...</p>';

            const tests = [];
            const serverIP = window.location.hostname === 'localhost' ? '*************' : window.location.hostname;

            // اختبار 1: الوصول للصفحة الرئيسية
            try {
                const response = await fetch(`http://${serverIP}:8000/`, {
                    method: 'HEAD',
                    mode: 'no-cors',
                    timeout: 5000
                });
                tests.push({
                    name: 'الوصول للصفحة الرئيسية',
                    status: 'success',
                    message: 'نجح الاتصال بالخادم'
                });
            } catch (error) {
                tests.push({
                    name: 'الوصول للصفحة الرئيسية',
                    status: 'error',
                    message: `فشل الاتصال: ${error.message}`
                });
            }

            // اختبار 2: اختبار API
            try {
                const response = await fetch(`http://${serverIP}:8000/health`, {
                    timeout: 5000
                });
                if (response.ok) {
                    tests.push({
                        name: 'اختبار API',
                        status: 'success',
                        message: 'API يعمل بشكل صحيح'
                    });
                } else {
                    tests.push({
                        name: 'اختبار API',
                        status: 'warning',
                        message: `استجابة غير متوقعة: ${response.status}`
                    });
                }
            } catch (error) {
                tests.push({
                    name: 'اختبار API',
                    status: 'error',
                    message: `فشل في الوصول للAPI: ${error.message}`
                });
            }

            // اختبار 3: معلومات الشبكة
            const networkInfo = {
                userAgent: navigator.userAgent,
                language: navigator.language,
                platform: navigator.platform,
                cookieEnabled: navigator.cookieEnabled,
                onLine: navigator.onLine
            };

            tests.push({
                name: 'معلومات الشبكة',
                status: navigator.onLine ? 'success' : 'warning',
                message: navigator.onLine ? 'متصل بالإنترنت' : 'غير متصل بالإنترنت'
            });

            // عرض النتائج
            let html = '';
            tests.forEach(test => {
                const iconMap = {
                    success: 'fa-check-circle',
                    warning: 'fa-exclamation-triangle',
                    error: 'fa-times-circle'
                };

                const colorMap = {
                    success: '#28a745',
                    warning: '#ffc107',
                    error: '#dc3545'
                };

                html += `
                    <div style="margin: 10px 0; padding: 10px; border-right: 4px solid ${colorMap[test.status]}; background: white; border-radius: 5px;">
                        <strong style="color: ${colorMap[test.status]};">
                            <i class="fas ${iconMap[test.status]}"></i> ${test.name}
                        </strong><br>
                        <span style="color: #666;">${test.message}</span>
                    </div>
                `;
            });

            // إضافة معلومات إضافية
            html += `
                <div style="margin-top: 20px; padding: 15px; background: #e3f2fd; border-radius: 8px;">
                    <h5 style="color: #1976d2; margin-bottom: 10px;">معلومات تشخيصية:</h5>
                    <ul style="margin: 0; padding-right: 20px; color: #666;">
                        <li>عنوان الخادم: ${serverIP}:8000</li>
                        <li>المتصفح: ${navigator.userAgent.split(' ')[0]}</li>
                        <li>النظام: ${navigator.platform}</li>
                        <li>اللغة: ${navigator.language}</li>
                        <li>الوقت: ${new Date().toLocaleString('ar-SA')}</li>
                    </ul>
                </div>
            `;

            // إضافة نصائح حل المشاكل
            const hasErrors = tests.some(test => test.status === 'error');
            if (hasErrors) {
                html += `
                    <div style="margin-top: 20px; padding: 15px; background: #fff3cd; border-radius: 8px; border: 1px solid #ffeaa7;">
                        <h5 style="color: #856404; margin-bottom: 10px;">نصائح لحل المشاكل:</h5>
                        <ul style="margin: 0; padding-right: 20px; color: #856404;">
                            <li>تأكد من أنك متصل بنفس شبكة Wi-Fi</li>
                            <li>جرب إعادة تحميل الصفحة</li>
                            <li>تحقق من أن الخادم يعمل</li>
                            <li>تواصل مع مدير النظام</li>
                        </ul>
                    </div>
                `;
            }

            resultsDiv.innerHTML = html;
        }
    </script>
</body>
</html>
