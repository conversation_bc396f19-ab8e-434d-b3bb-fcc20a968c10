/* متغيرات الثيم */
:root {
    --theme-primary: #667eea;
    --theme-secondary: #764ba2;
    --theme-background: #f8f9fa;
    --theme-surface: #ffffff;
    --theme-text: #333333;
}

/* إعدادات عامة */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    direction: rtl;
    overflow-x: hidden;
}

/* الصفحات */
.page {
    display: none;
    min-height: 100vh;
}

.page.active {
    display: block;
}

/* صفحة تسجيل الدخول */
.login-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    padding: 20px;
}

.login-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 40px;
    width: 100%;
    max-width: 450px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.login-header {
    text-align: center;
    margin-bottom: 30px;
}

.login-header i {
    font-size: 3rem;
    color: #667eea;
    margin-bottom: 15px;
}

.login-header h1 {
    color: #333;
    margin-bottom: 10px;
    font-weight: 700;
}

.login-header p {
    color: #666;
    font-size: 0.9rem;
}

/* التبويبات */
.login-tabs {
    display: flex;
    margin-bottom: 30px;
    background: #f8f9fa;
    border-radius: 10px;
    padding: 5px;
}

.tab-btn {
    flex: 1;
    padding: 12px;
    border: none;
    background: transparent;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 600;
    color: #666;
}

.tab-btn.active {
    background: #667eea;
    color: white;
    box-shadow: 0 2px 10px rgba(102, 126, 234, 0.3);
}

/* النماذج */
.auth-form {
    display: none;
}

.auth-form.active {
    display: block;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: #333;
    font-weight: 600;
    font-size: 0.9rem;
}

.input-group {
    position: relative;
    display: flex;
    align-items: center;
}

.input-group i {
    position: absolute;
    right: 15px;
    color: #999;
    z-index: 2;
}

.input-group input {
    width: 100%;
    padding: 15px 45px 15px 15px;
    border: 2px solid #e1e5e9;
    border-radius: 10px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: white;
}

.input-group input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.toggle-password {
    position: absolute;
    left: 15px;
    background: none;
    border: none;
    color: #999;
    cursor: pointer;
    z-index: 2;
}

.auth-btn {
    width: 100%;
    padding: 15px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 10px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.auth-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

.auth-btn:active {
    transform: translateY(0);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.2);
}

/* صفحة الدردشة */
.chat-container {
    height: 100vh;
    display: flex;
    flex-direction: column;
    background: white;
}

/* الشريط العلوي */
.chat-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.header-left {
    display: flex;
    align-items: center;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.user-avatar {
    width: 45px;
    height: 45px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
}

.user-details h3 {
    margin-bottom: 2px;
    font-size: 1.1rem;
}

.user-status {
    font-size: 0.8rem;
    opacity: 0.9;
}

.user-status.online {
    color: #4ade80;
}

.header-right {
    display: flex;
    gap: 10px;
}

.header-btn {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.2);
    border: none;
    border-radius: 50%;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.header-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

/* المحتوى الرئيسي */
.chat-main {
    flex: 1;
    display: flex;
    overflow: hidden;
}

/* الشريط الجانبي */
.chat-sidebar {
    width: 350px;
    background: #f8f9fa;
    border-left: 1px solid #e1e5e9;
    display: flex;
    flex-direction: column;
}

.sidebar-header {
    padding: 20px;
    border-bottom: 1px solid #e1e5e9;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.sidebar-header h3 {
    color: #333;
    font-weight: 700;
}

.header-actions {
    display: flex;
    gap: 8px;
}

.new-chat-btn,
.new-group-btn {
    width: 35px;
    height: 35px;
    background: #667eea;
    color: white;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.new-group-btn {
    background: #28a745;
}

.new-chat-btn:hover {
    background: #5a67d8;
    transform: scale(1.1);
}

.new-group-btn:hover {
    background: #218838;
    transform: scale(1.1);
}

.chat-search {
    padding: 15px 20px;
    border-bottom: 1px solid #e1e5e9;
}

.search-input {
    position: relative;
    display: flex;
    align-items: center;
}

.search-input i {
    position: absolute;
    right: 15px;
    color: #999;
    z-index: 2;
}

.search-input input {
    width: 100%;
    padding: 12px 45px 12px 15px;
    border: 1px solid #e1e5e9;
    border-radius: 25px;
    font-size: 0.9rem;
    background: white;
}

.search-input input:focus {
    outline: none;
    border-color: #667eea;
}

.chat-list {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    padding: 10px 0;
    min-height: 0; /* مهم للـ flexbox */
    scrollbar-width: thin;
    scrollbar-color: #c1c1c1 #f1f1f1;
}

.chat-list::-webkit-scrollbar {
    width: 6px;
}

.chat-list::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.chat-list::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.chat-list::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* منطقة الدردشة */
.chat-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: white;
}

.welcome-screen {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f8f9fa;
}

.welcome-content {
    text-align: center;
    color: #666;
}

.welcome-content i {
    font-size: 4rem;
    color: #667eea;
    margin-bottom: 20px;
}

.welcome-content h2 {
    margin-bottom: 10px;
    color: #333;
}

/* نافذة الدردشة */
.chat-window {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.chat-window-header {
    padding: 15px 20px;
    border-bottom: 1px solid #e1e5e9;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: white;
}

.chat-contact {
    display: flex;
    align-items: center;
    gap: 15px;
}

.contact-avatar {
    width: 40px;
    height: 40px;
    background: #667eea;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.contact-info h4 {
    margin-bottom: 2px;
    color: #333;
}

.contact-status {
    font-size: 0.8rem;
    color: #4ade80;
}

.chat-actions {
    display: flex;
    gap: 10px;
}

.action-btn {
    width: 35px;
    height: 35px;
    background: #f8f9fa;
    border: none;
    border-radius: 50%;
    color: #666;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.action-btn:hover {
    background: #e9ecef;
    color: #333;
}

/* منطقة الرسائل */
.messages-container {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    padding: 20px;
    background: #f8f9fa;
    scroll-behavior: smooth;
    min-height: 0; /* مهم للـ flexbox */
    height: 100%; /* تأكيد الارتفاع */
    max-height: calc(100vh - 200px); /* ارتفاع محدود */
}

/* الرسائل */
.message {
    margin-bottom: 15px;
    display: flex;
    align-items: flex-end;
    gap: 10px;
}

.message.sent {
    flex-direction: row-reverse;
}

.message-avatar {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: #667eea;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    flex-shrink: 0;
}

.message.sent .message-avatar {
    background: #28a745;
}

.message-content {
    max-width: 70%;
    position: relative;
}

.message-bubble {
    background: white;
    padding: 12px 16px;
    border-radius: 18px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    position: relative;
    word-wrap: break-word;
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.message-bubble:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transform: translateY(-1px);
}

.message.sent .message-bubble {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.message-bubble::before {
    content: '';
    position: absolute;
    bottom: 0;
    right: -8px;
    width: 0;
    height: 0;
    border: 8px solid transparent;
    border-top-color: white;
    border-right: 0;
    border-bottom: 0;
    margin-bottom: -8px;
}

.message.sent .message-bubble::before {
    right: auto;
    left: -8px;
    border-top-color: #667eea;
    border-left: 0;
    border-right: 8px solid transparent;
}

.message-time {
    font-size: 0.7rem;
    color: #999;
    margin-top: 5px;
    text-align: left;
}

.message.sent .message-time {
    text-align: right;
    color: rgba(255, 255, 255, 0.8);
}

.message-status {
    font-size: 0.7rem;
    color: #999;
    margin-top: 2px;
}

.message.sent .message-status {
    color: rgba(255, 255, 255, 0.8);
}

.message-status i {
    margin-left: 3px;
}

.message-status.delivered {
    color: #28a745;
}

.message-status.read {
    color: #007bff;
}

/* عناصر قائمة الدردشة - تصميم واتساب */
.chat-item {
    padding: 12px 16px;
    border-bottom: 1px solid #e9ecef;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    background: white;
    position: relative;
}

.chat-item:hover {
    background: #f5f5f5;
}

.chat-item.active {
    background: #e3f2fd;
    border-left: 4px solid #667eea;
}

/* الصورة الرمزية */
.chat-avatar {
    position: relative;
    width: 50px;
    height: 50px;
    margin-left: 12px;
    flex-shrink: 0;
}

.chat-avatar img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
}

.avatar-text {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: #667eea;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 18px;
}

.group-badge {
    position: absolute;
    bottom: -2px;
    right: -2px;
    background: #4caf50;
    color: white;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    border: 2px solid white;
}

/* محتوى المحادثة */
.chat-content {
    flex: 1;
    min-width: 0;
}

.chat-item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px;
}

.chat-item-name {
    font-weight: 600;
    color: #333;
    font-size: 0.95rem;
}

.chat-item-time {
    font-size: 0.8rem;
    color: #999;
}

.chat-item-preview {
    color: #666;
    font-size: 0.85rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.chat-item-badge {
    background: #667eea;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.7rem;
    font-weight: 600;
    margin-top: 5px;
}

/* قائمة المستخدمين */
.user-item {
    padding: 12px 20px;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 12px;
}

.user-item:hover {
    background: #f8f9fa;
}

.user-item-avatar {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    background: #667eea;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    flex-shrink: 0;
}

.user-item-info {
    flex: 1;
}

.user-item-name {
    font-weight: 600;
    color: #333;
    font-size: 0.9rem;
    margin-bottom: 2px;
}

.user-item-status {
    font-size: 0.75rem;
    color: #4ade80;
}

.user-item-status.offline {
    color: #999;
}

/* مؤشر الكتابة */
.typing-indicator {
    padding: 10px 20px;
    display: flex;
    align-items: center;
    gap: 10px;
    background: #f8f9fa;
}

.typing-dots {
    display: flex;
    gap: 3px;
}

.typing-dots span {
    width: 8px;
    height: 8px;
    background: #667eea;
    border-radius: 50%;
    animation: typing 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(1) { animation-delay: -0.32s; }
.typing-dots span:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing {
    0%, 80%, 100% { transform: scale(0); }
    40% { transform: scale(1); }
}

.typing-text {
    font-size: 0.8rem;
    color: #666;
}

/* منطقة إرسال الرسائل */
.message-input-container {
    padding: 15px 20px;
    background: white;
    border-top: 1px solid #e1e5e9;
    flex-shrink: 0; /* منع الانكماش */
    position: sticky;
    bottom: 0;
    z-index: 10;
}

.message-form {
    display: flex;
    align-items: center;
    gap: 10px;
}

.input-actions {
    display: flex;
    gap: 5px;
}

.input-btn {
    width: 35px;
    height: 35px;
    background: #f8f9fa;
    border: none;
    border-radius: 50%;
    color: #666;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.input-btn:hover {
    background: #e9ecef;
    color: #333;
}

.message-input {
    flex: 1;
}

.message-input input {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e1e5e9;
    border-radius: 25px;
    font-size: 0.9rem;
    background: #f8f9fa;
    transition: all 0.3s ease;
}

.message-input input:focus {
    outline: none;
    border-color: #667eea;
    background: white;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    transform: scale(1.02);
}

.send-btn {
    width: 40px;
    height: 40px;
    background: #667eea;
    color: white;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.send-btn:hover {
    background: #5a67d8;
    transform: scale(1.1);
}

/* النوافذ المنبثقة */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    justify-content: center;
    align-items: center;
}

.modal.active {
    display: flex;
}

.modal-content {
    background: white;
    border-radius: 15px;
    width: 90%;
    max-width: 500px;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.modal-header {
    padding: 20px;
    border-bottom: 1px solid #e1e5e9;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    color: #333;
    font-weight: 700;
}

.close-btn {
    width: 30px;
    height: 30px;
    background: none;
    border: none;
    color: #666;
    cursor: pointer;
    border-radius: 50%;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.close-btn:hover {
    background: #f8f9fa;
    color: #333;
}

.modal-body {
    padding: 20px;
    max-height: 60vh;
    overflow-y: auto;
}

/* التنبيهات */
.notifications-container {
    position: fixed;
    top: 20px;
    left: 20px;
    z-index: 2000;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.notification {
    background: white;
    border-radius: 10px;
    padding: 15px 20px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border-left: 4px solid #667eea;
    min-width: 300px;
    transform: translateX(-100%);
    animation: slideIn 0.3s ease forwards;
    display: flex;
    align-items: center;
    gap: 12px;
}

.notification.success {
    border-left-color: #28a745;
}

.notification.error {
    border-left-color: #dc3545;
}

.notification.warning {
    border-left-color: #ffc107;
}

.notification-icon {
    font-size: 1.2rem;
    color: #667eea;
}

.notification.success .notification-icon {
    color: #28a745;
}

.notification.error .notification-icon {
    color: #dc3545;
}

.notification.warning .notification-icon {
    color: #ffc107;
}

.notification-content {
    flex: 1;
}

.notification-title {
    font-weight: 600;
    color: #333;
    margin-bottom: 2px;
    font-size: 0.9rem;
}

.notification-message {
    color: #666;
    font-size: 0.8rem;
}

.notification-close {
    background: none;
    border: none;
    color: #999;
    cursor: pointer;
    font-size: 1rem;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.notification-close:hover {
    background: #f8f9fa;
    color: #333;
}

@keyframes slideIn {
    to {
        transform: translateX(0);
    }
}

@keyframes slideOut {
    to {
        transform: translateX(-100%);
    }
}

/* حالة التحميل */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.btn-loading {
    position: relative;
    color: transparent !important;
}

.btn-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 16px;
    height: 16px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* حالة الاتصال */
.connection-status {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: #28a745;
    color: white;
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
    z-index: 1000;
    transition: all 0.3s ease;
}

.connection-status.disconnected {
    background: #dc3545;
}

.connection-status.connecting {
    background: #ffc107;
    color: #333;
}

.connection-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: currentColor;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* تحسينات إضافية */
.scrollbar-thin::-webkit-scrollbar {
    width: 6px;
}

.scrollbar-thin::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

.messages-container,
.chat-list,
.modal-body {
    scrollbar-width: thin;
    scrollbar-color: #c1c1c1 #f1f1f1;
}

/* تحسين شريط التمرير للرسائل */
.messages-container::-webkit-scrollbar {
    width: 12px;
}

.messages-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 6px;
    margin: 5px 0;
}

.messages-container::-webkit-scrollbar-thumb {
    background: #667eea;
    border-radius: 6px;
    transition: background 0.3s ease;
    border: 2px solid #f1f1f1;
}

.messages-container::-webkit-scrollbar-thumb:hover {
    background: #5a6fd8;
}

.messages-container::-webkit-scrollbar-thumb:active {
    background: #4c63d2;
}

/* تأثيرات الحركة */
.fade-in {
    animation: fadeIn 0.3s ease;
}

.fade-out {
    animation: fadeOut 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes fadeOut {
    from { opacity: 1; transform: translateY(0); }
    to { opacity: 0; transform: translateY(-10px); }
}

.slide-up {
    animation: slideUp 0.3s ease;
}

@keyframes slideUp {
    from { transform: translateY(100%); }
    to { transform: translateY(0); }
}

/* منتقي الرموز التعبيرية */
.emoji-picker {
    position: fixed;
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    border: 1px solid #e1e5e9;
    width: 350px;
    max-height: 400px;
    z-index: 2000;
    overflow: hidden;
    animation: fadeIn 0.2s ease;
}

.emoji-picker-header {
    background: #f8f9fa;
    padding: 10px;
    border-bottom: 1px solid #e1e5e9;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.emoji-categories {
    display: flex;
    gap: 5px;
    overflow-x: auto;
    flex: 1;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.emoji-categories::-webkit-scrollbar {
    display: none;
}

.emoji-category-btn {
    background: none;
    border: none;
    padding: 8px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
    color: #666;
}

.emoji-category-btn:hover {
    background: #e9ecef;
}

.emoji-category-btn.active {
    background: #667eea;
    color: white;
}

.emoji-picker-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #999;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.emoji-picker-close:hover {
    background: #f0f0f0;
    color: #333;
}

.emoji-picker-content {
    padding: 10px;
}

.emoji-search {
    margin-bottom: 10px;
}

.emoji-search-input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #e1e5e9;
    border-radius: 20px;
    font-size: 0.9rem;
    outline: none;
    transition: all 0.3s ease;
}

.emoji-search-input:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
}

.emoji-grid {
    display: grid;
    grid-template-columns: repeat(8, 1fr);
    gap: 5px;
    max-height: 250px;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: #c1c1c1 #f1f1f1;
}

.emoji-grid::-webkit-scrollbar {
    width: 6px;
}

.emoji-grid::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.emoji-grid::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.emoji-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    padding: 8px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    aspect-ratio: 1;
}

.emoji-btn:hover {
    background: #f0f0f0;
    transform: scale(1.2);
}

.emoji-btn:active {
    transform: scale(1.1);
}

/* رفع الملفات */
.drop-zone {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    z-index: 3000;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: fadeIn 0.3s ease;
}

.drop-zone-content {
    background: white;
    border-radius: 20px;
    padding: 40px;
    text-align: center;
    max-width: 400px;
    width: 90%;
    border: 3px dashed #667eea;
    transition: all 0.3s ease;
}

.drop-zone.drag-over .drop-zone-content {
    border-color: #28a745;
    background: #f8fff9;
}

.drop-zone-content i {
    font-size: 4rem;
    color: #667eea;
    margin-bottom: 20px;
}

.drop-zone.drag-over .drop-zone-content i {
    color: #28a745;
}

.drop-zone-content h3 {
    color: #333;
    margin-bottom: 10px;
    font-weight: 600;
}

.drop-zone-content p {
    color: #666;
    margin-bottom: 20px;
    font-size: 0.9rem;
}

.drop-zone-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 25px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.drop-zone-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
}

/* معاينة الملفات */
.file-preview-container {
    display: none;
    padding: 15px 20px;
    background: #f8f9fa;
    border-top: 1px solid #e1e5e9;
    max-height: 200px;
    overflow-y: auto;
    gap: 10px;
    flex-wrap: wrap;
}

.file-preview {
    display: flex;
    align-items: center;
    background: white;
    border-radius: 12px;
    padding: 12px;
    border: 1px solid #e1e5e9;
    min-width: 250px;
    gap: 12px;
    transition: all 0.3s ease;
}

.file-preview:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.file-thumbnail {
    width: 50px;
    height: 50px;
    border-radius: 8px;
    background: #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    flex-shrink: 0;
}

.file-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 8px;
}

.file-thumbnail i {
    font-size: 1.5rem;
    color: #667eea;
}

.loading-placeholder {
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
    border-radius: 8px;
}

@keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

.file-info {
    flex: 1;
    min-width: 0;
}

.file-name {
    font-weight: 600;
    color: #333;
    font-size: 0.9rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-bottom: 4px;
}

.file-size {
    color: #666;
    font-size: 0.8rem;
}

.upload-progress {
    flex: 1;
    min-width: 0;
}

.progress-bar {
    width: 100%;
    height: 6px;
    background: #e1e5e9;
    border-radius: 3px;
    overflow: hidden;
    margin-bottom: 5px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea, #764ba2);
    width: 0%;
    transition: width 0.3s ease;
    animation: progressAnimation 2s infinite;
}

@keyframes progressAnimation {
    0% { width: 0%; }
    50% { width: 70%; }
    100% { width: 100%; }
}

.progress-text {
    font-size: 0.8rem;
    color: #666;
}

.file-actions {
    display: flex;
    gap: 8px;
}

.file-action-btn {
    width: 35px;
    height: 35px;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.9rem;
}

.send-btn {
    background: #28a745;
    color: white;
}

.send-btn:hover {
    background: #218838;
    transform: scale(1.1);
}

.remove-btn {
    background: #dc3545;
    color: white;
}

.remove-btn:hover {
    background: #c82333;
    transform: scale(1.1);
}

/* أنواع الرسائل المختلفة */
.message-text {
    word-wrap: break-word;
    line-height: 1.4;
}

/* رسائل الصور */
.image-message {
    max-width: 300px;
}

.image-message img {
    width: 100%;
    max-width: 300px;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.image-message img:hover {
    transform: scale(1.02);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.image-caption {
    margin-top: 8px;
    font-size: 0.9rem;
    color: inherit;
}

/* رسائل الملفات */
.file-message {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px;
    background: rgba(0, 0, 0, 0.05);
    border-radius: 8px;
    min-width: 200px;
}

.message.sent .file-message {
    background: rgba(255, 255, 255, 0.2);
}

.file-icon {
    width: 40px;
    height: 40px;
    background: #667eea;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
}

.file-details {
    flex: 1;
    min-width: 0;
}

.file-name {
    font-weight: 600;
    font-size: 0.9rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-bottom: 2px;
}

.file-info {
    font-size: 0.8rem;
    opacity: 0.8;
}

.file-download {
    width: 35px;
    height: 35px;
    background: rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: inherit;
    text-decoration: none;
    transition: all 0.3s ease;
}

.file-download:hover {
    background: rgba(0, 0, 0, 0.2);
    transform: scale(1.1);
}

/* رسائل صوتية */
.audio-message {
    display: flex;
    align-items: center;
    gap: 12px;
    min-width: 200px;
    padding: 8px;
}

.audio-play-btn {
    width: 40px;
    height: 40px;
    background: #667eea;
    border: none;
    border-radius: 50%;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.audio-play-btn:hover {
    background: #5a67d8;
    transform: scale(1.1);
}

.audio-waveform {
    flex: 1;
    height: 30px;
    background: rgba(0, 0, 0, 0.1);
    border-radius: 15px;
    position: relative;
    overflow: hidden;
}

.audio-progress {
    height: 100%;
    background: #667eea;
    border-radius: 15px;
    width: 0%;
    transition: width 0.1s ease;
}

.audio-duration {
    font-size: 0.8rem;
    opacity: 0.8;
    min-width: 35px;
    text-align: center;
}

/* رسائل فيديو */
.video-message {
    max-width: 300px;
}

.video-message video {
    width: 100%;
    max-width: 300px;
    border-radius: 12px;
}

.video-caption {
    margin-top: 8px;
    font-size: 0.9rem;
    color: inherit;
}

/* معاينة الرد */
.reply-preview {
    display: flex;
    gap: 8px;
    margin-bottom: 8px;
    padding: 8px;
    background: rgba(0, 0, 0, 0.05);
    border-radius: 8px;
    border-right: 3px solid #667eea;
}

.message.sent .reply-preview {
    background: rgba(255, 255, 255, 0.2);
    border-right-color: rgba(255, 255, 255, 0.5);
}

.reply-line {
    width: 3px;
    background: #667eea;
    border-radius: 2px;
}

.reply-content {
    flex: 1;
    min-width: 0;
}

.reply-sender {
    font-weight: 600;
    font-size: 0.8rem;
    color: #667eea;
    margin-bottom: 2px;
}

.message.sent .reply-sender {
    color: rgba(255, 255, 255, 0.9);
}

.reply-text {
    font-size: 0.8rem;
    opacity: 0.8;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* حالات الرسائل المحسنة */
.message-status {
    font-size: 0.7rem;
    margin-top: 2px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 2px;
}

.message-status.sent {
    color: rgba(255, 255, 255, 0.6);
}

.message-status.delivered {
    color: rgba(255, 255, 255, 0.8);
}

.message-status.read {
    color: #4ade80;
}

/* تحسينات للرسائل المختلفة */
.image-message,
.video-message {
    padding: 0;
}

.file-message,
.audio-message {
    background: transparent;
}

.message-bubble.image-message,
.message-bubble.video-message {
    padding: 4px;
    background: transparent;
    box-shadow: none;
}

.message-bubble.file-message,
.message-bubble.audio-message {
    padding: 12px;
}

/* نماذج المجموعات */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: 12px;
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 80px;
}

.selected-members {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin: 10px 0;
    min-height: 40px;
    padding: 8px;
    border: 1px dashed #e1e5e9;
    border-radius: 8px;
    background: #f8f9fa;
}

.selected-member {
    display: flex;
    align-items: center;
    gap: 8px;
    background: #667eea;
    color: white;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
}

.selected-member .remove-member {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    font-size: 0.7rem;
    padding: 0;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.selected-member .remove-member:hover {
    background: rgba(255, 255, 255, 0.2);
}

.members-list {
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    background: white;
}

.member-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    border-bottom: 1px solid #f0f0f0;
}

.member-item:last-child {
    border-bottom: none;
}

.member-item:hover {
    background: #f8f9fa;
}

.member-item.selected {
    background: #e3f2fd;
    color: #1976d2;
}

.member-avatar {
    width: 40px;
    height: 40px;
    background: #667eea;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
}

.member-info {
    flex: 1;
}

.member-name {
    font-weight: 600;
    margin-bottom: 2px;
}

.member-status {
    font-size: 0.8rem;
    color: #666;
}

.form-actions {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    margin-top: 24px;
    padding-top: 20px;
    border-top: 1px solid #e1e5e9;
}

.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
}

.btn-secondary {
    background: #f8f9fa;
    color: #666;
    border: 1px solid #e1e5e9;
}

.btn-secondary:hover {
    background: #e9ecef;
    color: #333;
}

/* شريط البحث */
.search-bar {
    background: #f8f9fa;
    border-bottom: 1px solid #e1e5e9;
    padding: 15px 20px;
    animation: slideDown 0.3s ease;
}

@keyframes slideDown {
    from {
        transform: translateY(-100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.search-input-container {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;
}

.search-input-container input {
    flex: 1;
    padding: 12px 15px;
    border: 1px solid #e1e5e9;
    border-radius: 25px;
    font-size: 0.9rem;
    outline: none;
    transition: all 0.3s ease;
}

.search-input-container input:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
}

.search-close-btn {
    background: #dc3545;
    color: white;
    border: none;
    width: 35px;
    height: 35px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.search-close-btn:hover {
    background: #c82333;
    transform: scale(1.1);
}

.search-results {
    max-height: 300px;
    overflow-y: auto;
    background: white;
    border-radius: 8px;
    border: 1px solid #e1e5e9;
}

.search-result-item {
    padding: 12px 15px;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer;
    transition: all 0.3s ease;
}

.search-result-item:last-child {
    border-bottom: none;
}

.search-result-item:hover {
    background: #f8f9fa;
}

.search-result-content {
    font-size: 0.9rem;
    line-height: 1.4;
    margin-bottom: 5px;
}

.search-result-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.8rem;
    color: #666;
}

.search-highlight {
    background: #fff3cd;
    padding: 2px 4px;
    border-radius: 3px;
    font-weight: 600;
}

.no-search-results {
    text-align: center;
    padding: 40px 20px;
    color: #666;
}

.no-search-results i {
    font-size: 3rem;
    margin-bottom: 15px;
    color: #ddd;
}

/* تحسينات للرسائل المفضلة والمؤرشفة */
.message-actions {
    position: absolute;
    top: 5px;
    left: 5px;
    display: none;
    gap: 5px;
}

.message:hover .message-actions {
    display: flex;
}

.message-action-btn {
    background: rgba(0, 0, 0, 0.7);
    color: white;
    border: none;
    width: 25px;
    height: 25px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.7rem;
}

.message-action-btn:hover {
    background: rgba(0, 0, 0, 0.9);
    transform: scale(1.1);
}

.message-action-btn.favorite {
    background: #ffc107;
    color: #333;
}

.message-action-btn.favorite:hover {
    background: #e0a800;
}

.message.favorited .message-bubble {
    border-right: 3px solid #ffc107;
}

.message.archived {
    opacity: 0.6;
}

/* الملف الشخصي والإعدادات */
.profile-modal {
    max-width: 700px;
    max-height: 90vh;
    overflow-y: auto;
}

.profile-section {
    margin-bottom: 30px;
}

.profile-avatar-section {
    text-align: center;
    margin-bottom: 30px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 15px;
}

.profile-avatar {
    width: 120px;
    height: 120px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
    font-weight: bold;
    margin-bottom: 15px;
    position: relative;
    overflow: hidden;
}

.profile-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
}

.change-avatar-btn {
    background: #667eea;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 8px;
    margin: 0 auto;
}

.change-avatar-btn:hover {
    background: #5a67d8;
    transform: translateY(-2px);
}

.profile-form {
    background: white;
    padding: 20px;
    border-radius: 15px;
    border: 1px solid #e1e5e9;
}

.profile-tabs {
    margin-top: 30px;
}

.tab-buttons {
    display: flex;
    background: #f8f9fa;
    border-radius: 10px;
    padding: 5px;
    margin-bottom: 20px;
}

.tab-btn {
    flex: 1;
    background: none;
    border: none;
    padding: 12px 20px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 600;
    color: #666;
}

.tab-btn.active {
    background: white;
    color: #667eea;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.tab-btn:hover:not(.active) {
    background: rgba(255, 255, 255, 0.5);
}

.tab-content {
    background: white;
    border-radius: 15px;
    border: 1px solid #e1e5e9;
}

.tab-panel {
    display: none;
    padding: 25px;
}

.tab-panel.active {
    display: block;
}

.settings-group {
    margin-bottom: 30px;
}

.settings-group h4 {
    color: #333;
    margin-bottom: 15px;
    font-size: 1.1rem;
    border-bottom: 2px solid #f0f0f0;
    padding-bottom: 8px;
}

.setting-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 0;
    border-bottom: 1px solid #f8f9fa;
}

.setting-item:last-child {
    border-bottom: none;
}

.setting-label {
    display: flex;
    align-items: center;
    gap: 12px;
    cursor: pointer;
    flex: 1;
}

.setting-text {
    color: #333;
    font-weight: 500;
}

.setting-label input[type="checkbox"] {
    width: 20px;
    height: 20px;
    accent-color: #667eea;
    cursor: pointer;
}

.setting-label select {
    padding: 8px 12px;
    border: 1px solid #e1e5e9;
    border-radius: 6px;
    background: white;
    color: #333;
    cursor: pointer;
    min-width: 120px;
}

.setting-label select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
}

/* تحسينات للوضع المظلم */
.dark-mode {
    background: #1a1a1a;
    color: #e0e0e0;
}

.dark-mode .sidebar {
    background: #2d2d2d;
    border-color: #404040;
}

.dark-mode .chat-area {
    background: #1e1e1e;
}

.dark-mode .message-bubble {
    background: #3d3d3d;
    color: #e0e0e0;
}

.dark-mode .message.sent .message-bubble {
    background: #667eea;
    color: white;
}

.dark-mode .modal-content {
    background: #2d2d2d;
    color: #e0e0e0;
}

.dark-mode .form-group input,
.dark-mode .form-group textarea,
.dark-mode .form-group select {
    background: #404040;
    border-color: #555;
    color: #e0e0e0;
}

.dark-mode .tab-buttons {
    background: #404040;
}

.dark-mode .tab-btn.active {
    background: #555;
    color: #667eea;
}

.dark-mode .tab-content {
    background: #2d2d2d;
    border-color: #404040;
}

.dark-mode .settings-group h4 {
    color: #e0e0e0;
    border-color: #404040;
}

.dark-mode .setting-text {
    color: #e0e0e0;
}

/* تحسينات أحجام الخط */
.font-small {
    font-size: 0.85rem;
}

.font-medium {
    font-size: 1rem;
}

.font-large {
    font-size: 1.15rem;
}

.font-small .message-bubble {
    font-size: 0.85rem;
}

.font-large .message-bubble {
    font-size: 1.15rem;
}

/* تأثيرات الانتقال للإعدادات */
.settings-transition {
    transition: all 0.3s ease;
}

/* تحسينات للاستجابة */
@media (max-width: 768px) {
    .profile-modal {
        max-width: 95%;
        margin: 10px;
    }

    .profile-avatar {
        width: 100px;
        height: 100px;
        font-size: 2.5rem;
    }

    .tab-buttons {
        flex-direction: column;
        gap: 5px;
    }

    .setting-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .setting-label {
        width: 100%;
        justify-content: space-between;
    }
}

/* إعدادات الإشعارات المتقدمة */
.quiet-hours-config {
    display: flex;
    gap: 15px;
    align-items: center;
    margin-top: 10px;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 8px;
}

.quiet-hours-config label {
    display: flex;
    flex-direction: column;
    gap: 5px;
    font-size: 0.9rem;
    color: #666;
}

.quiet-hours-config input[type="time"] {
    padding: 6px 10px;
    border: 1px solid #e1e5e9;
    border-radius: 6px;
    font-size: 0.9rem;
}

#volumeSlider {
    width: 100px;
    margin: 0 10px;
}

#volumeValue {
    font-size: 0.9rem;
    color: #667eea;
    font-weight: 600;
    min-width: 40px;
    
}

#testNotificationBtn {
    background: #28a745;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

#testNotificationBtn:hover {
    background: #218838;
    transform: translateY(-2px);
}

/* حالة الاتصال */
.connection-status {
    position: fixed;
    /* top: 20px; */
    right: 20px;
    background: #333;
    color: white;
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 0.8rem;
    z-index: 1000;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
}

.connection-status.connected {
    background: #28a745;
}

.connection-status.connecting {
    background: #ffc107;
    color: #333;
}

.connection-status.disconnected {
    background: #dc3545;
}

.connection-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: currentColor;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* إشعارات سطح المكتب */
.desktop-notification {
    position: fixed;
    top: 20px;
    left: 20px;
    background: white;
    border-radius: 10px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    padding: 15px;
    max-width: 300px;
    z-index: 2000;
    animation: slideInLeft 0.3s ease;
}

@keyframes slideInLeft {
    from {
        transform: translateX(-100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.desktop-notification .notification-header {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 8px;
}

.desktop-notification .notification-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #667eea;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
}

.desktop-notification .notification-title {
    font-weight: 600;
    color: #333;
}

.desktop-notification .notification-body {
    color: #666;
    font-size: 0.9rem;
    line-height: 1.4;
}

/* تحسينات للوضع المظلم */
.dark-mode .quiet-hours-config {
    background: #404040;
}

.dark-mode .quiet-hours-config label {
    color: #e0e0e0;
}

.dark-mode .quiet-hours-config input[type="time"] {
    background: #555;
    border-color: #666;
    color: #e0e0e0;
}

.dark-mode .desktop-notification {
    background: #2d2d2d;
    color: #e0e0e0;
}

.dark-mode .desktop-notification .notification-title {
    color: #e0e0e0;
}

.dark-mode .desktop-notification .notification-body {
    color: #ccc;
}

/* عارض الصور المحسن */
.image-viewer {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 3000;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.image-viewer.active {
    opacity: 1;
}

.image-viewer-backdrop {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    cursor: pointer;
}

.image-viewer-content {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.image-viewer-img {
    max-width: 90%;
    max-height: 90%;
    object-fit: contain;
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    transition: transform 0.3s ease;
}

.image-viewer-close {
    position: absolute;
    top: 20px;
    right: 20px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    border: none;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 1.2rem;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.image-viewer-close:hover {
    background: rgba(0, 0, 0, 0.9);
    transform: scale(1.1);
}

.image-viewer-controls {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 10px;
    background: rgba(0, 0, 0, 0.7);
    padding: 10px;
    border-radius: 25px;
}

.image-viewer-controls button {
    background: none;
    border: none;
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.image-viewer-controls button:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.1);
}

/* تحسينات الأداء */
.scrolling {
    pointer-events: none;
}

.scrolling .message {
    transform: translateZ(0);
}

.image-message img {
    opacity: 0;
    transition: opacity 0.3s ease;
}

.image-message img.loaded {
    opacity: 1;
}

.image-message img.error {
    opacity: 0.5;
    filter: grayscale(100%);
}

/* تحسينات التحميل */
.loading-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
    border-radius: 4px;
}

.message-skeleton {
    height: 60px;
    margin: 10px 0;
    border-radius: 12px;
}

.avatar-skeleton {
    width: 40px;
    height: 40px;
    border-radius: 50%;
}

.text-skeleton {
    height: 16px;
    margin: 4px 0;
}

.text-skeleton.short {
    width: 60%;
}

.text-skeleton.medium {
    width: 80%;
}

.text-skeleton.long {
    width: 100%;
}

/* تحسينات الاستجابة */
@media (max-width: 768px) {
    .image-viewer-content {
        padding: 10px;
    }

    .image-viewer-img {
        max-width: 95%;
        max-height: 95%;
    }

    .image-viewer-close {
        top: 10px;
        right: 10px;
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }

    .image-viewer-controls {
        bottom: 10px;
        padding: 8px;
    }

    .image-viewer-controls button {
        width: 35px;
        height: 35px;
        font-size: 0.9rem;
    }
}

/* تحسينات الوضع المظلم للعارض */
.dark-mode .image-viewer-backdrop {
    background: rgba(0, 0, 0, 0.95);
}

.dark-mode .loading-skeleton {
    background: linear-gradient(90deg, #404040 25%, #505050 50%, #404040 75%);
    background-size: 200% 100%;
}

/* تحسينات الانتقالات */
.smooth-transition {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.fade-in {
    animation: fadeIn 0.3s ease;
}

.slide-up {
    animation: slideUp 0.3s ease;
}

.scale-in {
    animation: scaleIn 0.3s ease;
}

@keyframes scaleIn {
    from {
        transform: scale(0.8);
        opacity: 0;
    }
    to {
        transform: scale(1);
        opacity: 1;
    }
}

/* تحسينات الأداء للرسائل */
.message-list {
    contain: layout style paint;
}

.message {
    contain: layout style;
    will-change: transform;
}

.message-bubble {
    contain: layout style paint;
}

/* تحسينات للتمرير السلس */
.smooth-scroll {
    scroll-behavior: smooth;
}

.messages-container {
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
    /* تحسينات إضافية للتمرير */
    overscroll-behavior: contain;
    scroll-padding: 20px;
}

/* إضافة مؤشر بصري للتمرير */
.messages-container.scrolling {
    box-shadow: inset 0 0 10px rgba(102, 126, 234, 0.1);
}

/* تحسينات للتركيز */
.focus-visible {
    outline: 2px solid #667eea;
    outline-offset: 2px;
}

/* أزرار تعديل وحذف الرسائل */
.message-actions {
    display: none;
    position: absolute;
    top: 5px;
    left: 5px;
    gap: 5px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 15px;
    padding: 3px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.message.sent:hover .message-actions {
    display: flex;
}

.message-action-btn {
    width: 25px;
    height: 25px;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    transition: all 0.2s ease;
    background: transparent;
}

.message-action-btn:hover {
    transform: scale(1.1);
}

.edit-btn {
    color: #28a745;
}

.edit-btn:hover {
    background: #28a745;
    color: white;
}

.delete-btn {
    color: #dc3545;
}

.delete-btn:hover {
    background: #dc3545;
    color: white;
}

.message-bubble {
    position: relative;
}

/* تحسين عرض الرسائل المعدلة */
.message-time {
    font-size: 0.75rem;
    color: #999;
    margin-top: 5px;
}

/* قائمة خيارات المحادثات */
.chat-header-actions {
    display: flex;
    align-items: center;
    gap: 10px;
}

.chat-options {
    position: relative;
}

.chat-options-btn {
    background: none;
    border: none;
    color: #999;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    width: 25px;
    height: 25px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    opacity: 0;
}

.chat-item:hover .chat-options-btn {
    opacity: 1;
}

.chat-options-btn:hover {
    background: #f0f0f0;
    color: #333;
}

.chat-options-menu {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    background: white;
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    min-width: 150px;
    overflow: hidden;
}

.chat-options-menu button {
    width: 100%;
    padding: 10px 15px;
    border: none;
    background: none;
    text-align: right;
    cursor: pointer;
    transition: background 0.2s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
}

.chat-options-menu button:hover {
    background: #f8f9fa;
}

.chat-options-menu button:first-child:hover {
    background: #fff5f5;
    color: #dc3545;
}

.chat-options-menu button i {
    width: 16px;
    text-align: center;
}

/* صفحة البروفايل */
.profile-modal {
    max-width: 500px;
    width: 90%;
}

.profile-section {
    padding: 20px 0;
}

.profile-avatar-section {
    text-align: center;
    margin-bottom: 30px;
}

.profile-avatar {
    width: 120px;
    height: 120px;
    margin: 0 auto 15px;
    position: relative;
    border-radius: 50%;
    overflow: hidden;
    border: 4px solid #667eea;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.profile-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.profile-avatar .avatar-text {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 3rem;
    font-weight: bold;
}

.change-avatar-btn {
    background: #667eea;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 25px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.change-avatar-btn:hover {
    background: #5a6fd8;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.profile-form {
    max-width: 400px;
    margin: 0 auto;
}

.profile-form .form-group {
    margin-bottom: 20px;
}

.profile-form label {
    display: block;
    margin-bottom: 8px;
    color: #333;
    font-weight: 600;
    font-size: 0.9rem;
}

.profile-form input,
.profile-form textarea {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
    font-family: inherit;
}

.profile-form input:focus,
.profile-form textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.profile-form input[readonly] {
    background: #f8f9fa;
    color: #6c757d;
}

.profile-form textarea {
    resize: vertical;
    min-height: 80px;
}

.form-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-top: 30px;
}

.form-actions .btn {
    flex: 1;
    max-width: 180px;
}

/* مؤشر تحميل الرسائل القديمة */
.loading-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    padding: 15px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 10px;
    margin: 10px 0;
    color: #667eea;
    font-size: 0.9rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.loading-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* مؤشر نهاية المحادثة */
.end-of-messages-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    padding: 15px;
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
    border-radius: 15px;
    margin: 10px 0;
    font-size: 0.9rem;
    font-weight: 600;
    box-shadow: 0 4px 15px rgba(79, 172, 254, 0.3);
    animation: slideInFromTop 0.5s ease-out;
}

.end-icon {
    font-size: 1.2rem;
    animation: bounce 1s ease-in-out infinite;
}

@keyframes slideInFromTop {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-5px);
    }
    60% {
        transform: translateY(-3px);
    }
}

/* 😀 لوحة الرموز التعبيرية الرائعة */
.emoji-panel {
    position: absolute;
    bottom: 70px;
    right: 20px;
    width: 350px;
    height: 400px;
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    border: 1px solid #e1e5e9;
    display: none;
    flex-direction: column;
    z-index: 1000;
    animation: emojiPanelSlideUp 0.3s ease-out;
}

@keyframes emojiPanelSlideUp {
    from {
        opacity: 0;
        transform: translateY(20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.emoji-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    border-bottom: 1px solid #f0f0f0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 15px 15px 0 0;
}

.emoji-tabs {
    display: flex;
    gap: 5px;
}

.emoji-tab {
    background: none;
    border: none;
    font-size: 1.2rem;
    padding: 8px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    opacity: 0.7;
}

.emoji-tab:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.1);
}

.emoji-tab.active {
    background: rgba(255, 255, 255, 0.3);
    opacity: 1;
    transform: scale(1.1);
}

.emoji-close {
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.emoji-close:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: rotate(90deg);
}

.emoji-content {
    flex: 1;
    padding: 15px;
    overflow-y: auto;
    display: grid;
    grid-template-columns: repeat(8, 1fr);
    gap: 8px;
    align-content: start;
}

.emoji-item {
    font-size: 1.5rem;
    padding: 8px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    text-align: center;
    user-select: none;
}

.emoji-item:hover {
    background: #f0f8ff;
    transform: scale(1.2);
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.emoji-search {
    padding: 10px 15px;
    border-top: 1px solid #f0f0f0;
}

.emoji-search input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #e1e5e9;
    border-radius: 20px;
    font-size: 0.9rem;
    outline: none;
    transition: all 0.2s ease;
}

.emoji-search input:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* تحسين شريط التمرير للرموز التعبيرية */
.emoji-content::-webkit-scrollbar {
    width: 6px;
}

.emoji-content::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.emoji-content::-webkit-scrollbar-thumb {
    background: #667eea;
    border-radius: 3px;
}

.emoji-content::-webkit-scrollbar-thumb:hover {
    background: #5a6fd8;
}

/* 📁 تصميم الملفات والصور */
.file-message {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 12px;
    border: 1px solid #e9ecef;
    max-width: 300px;
    transition: all 0.2s ease;
}

.file-message:hover {
    background: #e9ecef;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.file-message.uploading {
    opacity: 0.7;
    pointer-events: none;
}

.file-icon {
    font-size: 2rem;
    flex-shrink: 0;
}

.file-info {
    flex: 1;
    min-width: 0;
}

.file-name {
    font-weight: 600;
    color: #333;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-bottom: 4px;
}

.file-size {
    font-size: 0.8rem;
    color: #666;
}

.file-download {
    background: #667eea;
    color: white;
    border: none;
    border-radius: 50%;
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
}

.file-download:hover {
    background: #5a6fd8;
    transform: scale(1.1);
}

.upload-progress {
    width: 100%;
    height: 4px;
    background: #e9ecef;
    border-radius: 2px;
    overflow: hidden;
    margin-top: 8px;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #667eea, #764ba2);
    border-radius: 2px;
    animation: progressAnimation 2s ease-in-out infinite;
}

@keyframes progressAnimation {
    0% { width: 0%; }
    50% { width: 70%; }
    100% { width: 100%; }
}

/* تصميم الصور */
.image-message {
    max-width: 300px;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
}

.image-message:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}

.image-message img {
    width: 100%;
    height: auto;
    display: block;
    cursor: pointer;
    transition: all 0.2s ease;
}

.image-message img:hover {
    transform: scale(1.02);
}

.image-info {
    padding: 10px;
    background: white;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.image-name {
    font-size: 0.9rem;
    font-weight: 600;
    color: #333;
}

.image-size {
    font-size: 0.8rem;
    color: #666;
}

/* عارض الصور */
.image-viewer {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 2000;
}

.image-viewer-backdrop {
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
}

.image-viewer-content {
    position: relative;
    max-width: 90%;
    max-height: 90%;
    cursor: default;
}

.image-viewer-content img {
    max-width: 100%;
    max-height: 100%;
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
}

.image-viewer-close {
    position: absolute;
    top: -40px;
    right: 0;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: none;
    border-radius: 50%;
    width: 35px;
    height: 35px;
    font-size: 1.2rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.image-viewer-close:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

/* تأثير السحب والإفلات */
.drag-over {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
    border: 2px dashed #667eea;
    border-radius: 10px;
}

.drag-over::after {
    content: '📁 اسحب الملفات هنا لرفعها';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(102, 126, 234, 0.9);
    color: white;
    padding: 20px 30px;
    border-radius: 10px;
    font-size: 1.1rem;
    font-weight: 600;
    z-index: 100;
    pointer-events: none;
}

/* 🎵 تصميم الرسائل الصوتية */
.audio-message {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 15px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 20px;
    max-width: 250px;
    color: white;
    transition: all 0.2s ease;
}

.audio-message:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.audio-message.uploading {
    opacity: 0.7;
    background: linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%);
}

.audio-play-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    color: white;
    flex-shrink: 0;
}

.audio-play-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.audio-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.audio-waveform {
    display: flex;
    align-items: center;
    gap: 2px;
    height: 20px;
}

.waveform-bar {
    width: 3px;
    background: rgba(255, 255, 255, 0.6);
    border-radius: 2px;
    animation: waveformAnimation 1.5s ease-in-out infinite;
}

.waveform-bar:nth-child(1) { height: 8px; animation-delay: 0s; }
.waveform-bar:nth-child(2) { height: 16px; animation-delay: 0.1s; }
.waveform-bar:nth-child(3) { height: 12px; animation-delay: 0.2s; }
.waveform-bar:nth-child(4) { height: 20px; animation-delay: 0.3s; }
.waveform-bar:nth-child(5) { height: 6px; animation-delay: 0.4s; }

@keyframes waveformAnimation {
    0%, 100% { transform: scaleY(1); }
    50% { transform: scaleY(0.5); }
}

.audio-duration {
    font-size: 0.8rem;
    opacity: 0.8;
}

.audio-text {
    font-size: 0.9rem;
    font-weight: 500;
}

/* تأثير التسجيل */
.input-btn.recording {
    background: #e74c3c !important;
    animation: recordingPulse 1s ease-in-out infinite;
}

@keyframes recordingPulse {
    0% { transform: scale(1); box-shadow: 0 0 0 0 rgba(231, 76, 60, 0.7); }
    70% { transform: scale(1.05); box-shadow: 0 0 0 10px rgba(231, 76, 60, 0); }
    100% { transform: scale(1); box-shadow: 0 0 0 0 rgba(231, 76, 60, 0); }
}

/* 🔍 تصميم البحث المتقدم */
.search-input input:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    background: white;
}

.no-search-results {
    text-align: center;
    padding: 40px 20px;
    color: #666;
}

.no-results-icon {
    font-size: 3rem;
    margin-bottom: 15px;
    opacity: 0.6;
}

.no-search-results h3 {
    margin: 0 0 10px 0;
    font-size: 1.2rem;
    color: #333;
}

.no-search-results p {
    margin: 0 0 20px 0;
    font-size: 0.9rem;
}

.clear-search-btn {
    background: #667eea;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 20px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.clear-search-btn:hover {
    background: #5a6fd8;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

/* تمييز نتائج البحث */
mark {
    background: linear-gradient(135deg, #ffeaa7, #fdcb6e);
    color: #2d3436;
    padding: 2px 4px;
    border-radius: 3px;
    font-weight: 600;
}

/* تحسين البحث في الشريط الجانبي */
.chat-search {
    position: relative;
}

.chat-search::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 15px;
    right: 15px;
    height: 1px;
    background: linear-gradient(90deg, transparent, #667eea, transparent);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.chat-search:focus-within::after {
    opacity: 1;
}

/* 🎨 نظام الثيمات */
.theme-modal .modal-content {
    max-width: 600px;
    width: 90%;
}

.theme-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.theme-option {
    text-align: center;
    cursor: pointer;
    padding: 15px;
    border-radius: 12px;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    position: relative;
}

.theme-option:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.theme-option.active {
    border-color: var(--theme-primary);
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.theme-preview {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    margin: 0 auto 10px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
}

.theme-option:hover .theme-preview {
    transform: scale(1.1);
}

.theme-name {
    font-weight: 600;
    color: var(--theme-text);
    margin-bottom: 5px;
}

.theme-check {
    position: absolute;
    top: 10px;
    right: 10px;
    color: var(--theme-primary);
    font-size: 1.2rem;
    animation: checkmarkAppear 0.3s ease;
}

@keyframes checkmarkAppear {
    from {
        opacity: 0;
        transform: scale(0.5);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

.theme-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-top: 30px;
}

/* الوضع الليلي */
.theme-dark {
    background: #1a1a1a !important;
    color: #ffffff;
}

.theme-dark .chat-container {
    background: #2d2d2d;
}

.theme-dark .sidebar {
    background: #2d2d2d;
    border-color: #404040;
}

.theme-dark .chat-item {
    border-color: #404040;
}

.theme-dark .chat-item:hover {
    background: #404040;
}

.theme-dark .messages-container {
    background: #1a1a1a;
}

.theme-dark .message-input-container {
    background: #2d2d2d;
    border-color: #404040;
}

.theme-dark .message-input {
    background: #404040;
    color: #ffffff;
    border-color: #555555;
}

.theme-dark .message.sent .message-bubble {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.theme-dark .message.received .message-bubble {
    background: #404040;
    color: #ffffff;
}

/* تطبيق متغيرات الثيم */
body {
    background: linear-gradient(135deg, var(--theme-primary) 0%, var(--theme-secondary) 100%);
}

.btn-primary {
    background: var(--theme-primary);
}

.btn-primary:hover {
    background: var(--theme-secondary);
}

.sidebar-header {
    background: var(--theme-primary);
}

.emoji-header {
    background: linear-gradient(135deg, var(--theme-primary) 0%, var(--theme-secondary) 100%);
}

/* ⌨️ حالة الكتابة */
.typing-status {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.8rem;
    color: #667eea;
    margin-top: 2px;
    animation: typingAppear 0.3s ease-in;
}

@keyframes typingAppear {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.typing-text {
    font-weight: 500;
}

.typing-dots {
    display: flex;
    gap: 3px;
}

.typing-dots span {
    width: 4px;
    height: 4px;
    background: #667eea;
    border-radius: 50%;
    animation: typingDots 1.4s ease-in-out infinite;
}

.typing-dots span:nth-child(1) {
    animation-delay: 0s;
}

.typing-dots span:nth-child(2) {
    animation-delay: 0.2s;
}

.typing-dots span:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes typingDots {
    0%, 60%, 100% {
        transform: scale(1);
        opacity: 0.5;
    }
    30% {
        transform: scale(1.2);
        opacity: 1;
    }
}

/* إخفاء حالة الاتصال عند الكتابة */
.typing-status:not([style*="display: none"]) ~ .contact-status {
    display: none;
}

/* تحسين عرض معلومات الاتصال */
.contact-info {
    position: relative;
}

.contact-status {
    transition: opacity 0.3s ease;
}

/* تحسينات للوصولية */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* تحسينات للطباعة */
@media print {
    .sidebar,
    .message-input-container,
    .chat-actions,
    .notifications-container {
        display: none !important;
    }

    .chat-area {
        width: 100% !important;
        margin: 0 !important;
    }

    .message {
        break-inside: avoid;
    }
}

/* تحسينات التصميم النهائية */
.app-container {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    position: relative;
}

.app-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="10" cy="50" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="90" cy="30" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
    pointer-events: none;
}

/* تحسينات الرسائل */
.message-bubble {
    position: relative;
    overflow: hidden;
}

.message-bubble::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s ease;
}

.message:hover .message-bubble::before {
    left: 100%;
}

/* تأثيرات الكتابة */
.typing-indicator {
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 10px 15px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    margin: 10px 0;
    animation: pulse 2s infinite;
}

.typing-dots {
    display: flex;
    gap: 3px;
}

.typing-dot {
    width: 6px;
    height: 6px;
    background: #667eea;
    border-radius: 50%;
    animation: typingDot 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(1) { animation-delay: -0.32s; }
.typing-dot:nth-child(2) { animation-delay: -0.16s; }
.typing-dot:nth-child(3) { animation-delay: 0s; }

@keyframes typingDot {
    0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

/* تحسينات الأزرار */
.btn {
    position: relative;
    overflow: hidden;
    transform: translateZ(0);
}

.btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.3s ease, height 0.3s ease;
}

.btn:hover::before {
    width: 300px;
    height: 300px;
}

/* تحسينات الشريط الجانبي */
.sidebar {
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.conversation-item {
    position: relative;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.conversation-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 3px;
    background: #667eea;
    transform: scaleY(0);
    transition: transform 0.3s ease;
}

.conversation-item:hover::before,
.conversation-item.active::before {
    transform: scaleY(1);
}

/* تحسينات الإشعارات */
.notification {
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* تحسينات النوافذ المنبثقة */
.modal {
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
}

.modal-content {
    transform: scale(0.9);
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modal.active .modal-content {
    transform: scale(1);
}

/* تحسينات شريط التمرير */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: rgba(102, 126, 234, 0.6);
    border-radius: 4px;
    transition: background 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(102, 126, 234, 0.8);
}

/* تحسينات للوضع المظلم */
.dark-mode .app-container {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
}

.dark-mode .typing-dot {
    background: #3498db;
}

.dark-mode ::-webkit-scrollbar-thumb {
    background: rgba(52, 152, 219, 0.6);
}

.dark-mode ::-webkit-scrollbar-thumb:hover {
    background: rgba(52, 152, 219, 0.8);
}

/* تأثيرات الانتقال المتقدمة */
.page-transition {
    animation: pageSlideIn 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes pageSlideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* تحسينات الاستجابة المتقدمة */
@media (max-width: 480px) {
    .message-bubble {
        max-width: 85%;
        font-size: 0.9rem;
    }

    .chat-header {
        padding: 10px 15px;
    }

    .message-input-container {
        padding: 10px;
    }

    .sidebar {
        width: 100%;
        transform: translateX(-100%);
    }

    .sidebar.active {
        transform: translateX(0);
    }
}

/* تحسينات للأجهزة اللوحية */
@media (min-width: 481px) and (max-width: 1024px) {
    .sidebar {
        width: 280px;
    }

    .chat-area {
        margin-right: 280px;
    }

    .message-bubble {
        max-width: 75%;
    }
}

/* تحسينات للشاشات الكبيرة */
@media (min-width: 1400px) {
    .app-container {
        max-width: 1400px;
        margin: 0 auto;
        box-shadow: 0 0 50px rgba(0, 0, 0, 0.1);
    }

    .sidebar {
        width: 350px;
    }

    .chat-area {
        margin-right: 350px;
    }
}

/* تحسينات إضافية للتفاعل */
.interactive-element {
    transition: all 0.2s ease;
    cursor: pointer;
}

.interactive-element:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.interactive-element:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

/* تأثيرات الإضاءة */
.glow-effect {
    box-shadow: 0 0 20px rgba(102, 126, 234, 0.3);
    transition: box-shadow 0.3s ease;
}

.glow-effect:hover {
    box-shadow: 0 0 30px rgba(102, 126, 234, 0.5);
}

/* أنماط المجموعات في قائمة المحادثات */
.chat-item.conversation-item[data-is-group="true"] {
    border-left: 3px solid #28a745;
}

.chat-item-avatar.group-avatar {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
}

.group-icon {
    font-size: 0.8rem;
    color: #28a745;
    margin-right: 5px;
}

.chat-item-status {
    font-size: 0.75rem;
    margin-top: 2px;
}

.status-indicator {
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 0.7rem;
    font-weight: 500;
}

.status-indicator.online {
    background: #d4edda;
    color: #155724;
}

.status-indicator.offline {
    background: #f8d7da;
    color: #721c24;
}

.status-indicator.group {
    background: #d1ecf1;
    color: #0c5460;
}

.chat-contact-status.group {
    color: #28a745;
}

.chat-contact-status.online {
    color: #28a745;
}

.chat-contact-status.offline {
    color: #6c757d;
}

/* تحسينات للرسائل في المجموعات */
.message.group-message .message-sender {
    font-size: 0.8rem;
    color: #667eea;
    font-weight: 600;
    margin-bottom: 2px;
}

.message.group-message .message-bubble {
    margin-top: 5px;
}

/* تحسينات عامة للمحادثات */
.chat-item:hover {
    background: rgba(102, 126, 234, 0.05);
    transform: translateX(5px);
}

.chat-item.active {
    background: rgba(102, 126, 234, 0.1);
    border-left: 4px solid #667eea;
}

.chat-item-badge {
    background: #dc3545;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.7rem;
    font-weight: bold;
    position: absolute;
    top: 10px;
    left: 10px;
}

/* CSS إضافي للتصميم الجديد - واتساب ستايل */
.chat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 4px;
}

.chat-name {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #333;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.chat-time {
    font-size: 12px;
    color: #999;
    white-space: nowrap;
}

.chat-preview {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.preview-text {
    font-size: 14px;
    color: #666;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    flex: 1;
}

.unread-badge {
    background: #667eea;
    color: white;
    border-radius: 12px;
    padding: 2px 8px;
    font-size: 11px;
    font-weight: bold;
    min-width: 18px;
    text-align: center;
    margin-right: 4px;
}

.no-chats-message {
    text-align: center;
    padding: 40px 20px;
    color: #999;
}

.no-chats-message i {
    font-size: 3rem;
    margin-bottom: 16px;
    color: #667eea;
    opacity: 0.5;
}

.no-chats-message h3 {
    margin: 0 0 8px 0;
    font-size: 18px;
    color: #333;
}

.no-chats-message p {
    margin: 0;
    font-size: 14px;
}

/* رسائل الخطأ */
.error-message {
    text-align: center;
    padding: 40px 20px;
    color: #e74c3c;
}

.error-message i {
    font-size: 3rem;
    margin-bottom: 16px;
    color: #e74c3c;
}

.error-message h3 {
    margin: 0 0 8px 0;
    font-size: 18px;
    color: #e74c3c;
}

.error-message p {
    margin: 0 0 16px 0;
    font-size: 14px;
    color: #666;
}

.retry-btn {
    background: #667eea;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    transition: background 0.2s ease;
}

.retry-btn:hover {
    background: #5a6fd8;
}

.retry-btn i {
    margin-left: 8px;
}

.chat-item {
    position: relative;
    transition: all 0.3s ease;
}

/* التجاوب */
@media (max-width: 768px) {
    .chat-sidebar {
        width: 100%;
        position: absolute;
        top: 0;
        right: -100%;
        height: 100%;
        z-index: 100;
        transition: right 0.3s ease;
    }
    
    .chat-sidebar.active {
        right: 0;
    }
    
    .login-card {
        padding: 30px 20px;
    }
    
    .modal-content {
        width: 95%;
        margin: 20px;
    }
}
