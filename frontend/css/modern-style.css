/* 🎨 تصميم رائع وجميل للتطبيق */

/* متغيرات CSS - ألوان جميلة */
:root {
    /* ألوان أساسية رائعة */
    --primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --warning: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
    --error: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
    
    /* ألوان النص */
    --text-primary: #1a202c;
    --text-secondary: #718096;
    --text-light: #a0aec0;
    --text-white: #ffffff;
    
    /* ألوان الخلفية */
    --bg-primary: #f7fafc;
    --bg-white: #ffffff;
    --bg-gray: #edf2f7;
    --bg-dark: #2d3748;
    
    /* ظلال جميلة */
    --shadow-sm: 0 2px 4px rgba(0,0,0,0.1);
    --shadow-md: 0 4px 12px rgba(0,0,0,0.15);
    --shadow-lg: 0 8px 25px rgba(0,0,0,0.15);
    --shadow-xl: 0 20px 40px rgba(0,0,0,0.1);
    
    /* انتقالات سلسة */
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-fast: all 0.15s ease;
}

/* إعدادات عامة */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: var(--primary);
    min-height: 100vh;
    direction: rtl;
    overflow-x: hidden;
    line-height: 1.6;
}

/* الصفحات */
.page {
    display: none;
    min-height: 100vh;
    transition: var(--transition);
}

.page.active {
    display: flex;
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* صفحة تسجيل الدخول - تصميم رائع */
.login-page {
    justify-content: center;
    align-items: center;
    padding: 20px;
}

.login-container {
    background: var(--bg-white);
    border-radius: 20px;
    box-shadow: var(--shadow-xl);
    padding: 40px;
    width: 100%;
    max-width: 400px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.2);
}

.login-header {
    text-align: center;
    margin-bottom: 30px;
}

.login-header h1 {
    background: var(--primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 10px;
}

.login-header p {
    color: var(--text-secondary);
    font-size: 1.1rem;
}

/* النماذج الجميلة */
.form-group {
    margin-bottom: 20px;
    position: relative;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: var(--text-primary);
    font-weight: 600;
    font-size: 0.9rem;
}

.form-group input {
    width: 100%;
    padding: 15px 20px;
    border: 2px solid var(--bg-gray);
    border-radius: 12px;
    font-size: 1rem;
    transition: var(--transition);
    background: var(--bg-white);
    color: var(--text-primary);
}

.form-group input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    transform: translateY(-2px);
}

/* الأزرار الجميلة */
.btn {
    padding: 15px 30px;
    border: none;
    border-radius: 12px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    position: relative;
    overflow: hidden;
}

.btn-primary {
    background: var(--primary);
    color: var(--text-white);
    box-shadow: var(--shadow-md);
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-lg);
}

.btn-primary:active {
    transform: translateY(-1px);
}

.btn-secondary {
    background: var(--bg-gray);
    color: var(--text-primary);
    border: 2px solid var(--bg-gray);
}

.btn-secondary:hover {
    background: var(--bg-white);
    border-color: #667eea;
    color: #667eea;
}

/* صفحة الدردشة - تصميم احترافي */
.chat-page {
    background: var(--bg-primary);
}

.chat-container {
    display: flex;
    height: 100vh;
    max-width: 1400px;
    margin: 0 auto;
    box-shadow: var(--shadow-xl);
    border-radius: 0;
    overflow: hidden;
}

/* الشريط الجانبي الجميل */
.sidebar {
    width: 350px;
    background: var(--bg-white);
    border-left: 1px solid var(--bg-gray);
    display: flex;
    flex-direction: column;
    position: relative;
}

.sidebar-header {
    padding: 20px;
    background: var(--primary);
    color: var(--text-white);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.sidebar-header h3 {
    font-size: 1.3rem;
    font-weight: 700;
}

.header-actions {
    display: flex;
    gap: 10px;
}

.new-chat-btn, .new-group-btn {
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 50%;
    background: rgba(255,255,255,0.2);
    color: var(--text-white);
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
}

.new-chat-btn:hover, .new-group-btn:hover {
    background: rgba(255,255,255,0.3);
    transform: scale(1.1);
}

/* البحث الجميل */
.chat-search {
    padding: 15px 20px;
    background: var(--bg-white);
    border-bottom: 1px solid var(--bg-gray);
}

.search-input {
    position: relative;
}

.search-input i {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-light);
}

.search-input input {
    width: 100%;
    padding: 12px 45px 12px 15px;
    border: 2px solid var(--bg-gray);
    border-radius: 25px;
    font-size: 0.9rem;
    transition: var(--transition);
    background: var(--bg-gray);
}

.search-input input:focus {
    outline: none;
    border-color: #667eea;
    background: var(--bg-white);
    box-shadow: var(--shadow-sm);
}

/* قائمة المحادثات الجميلة */
.chat-list {
    flex: 1;
    overflow-y: auto;
    padding: 0;
}

.chat-item {
    padding: 15px 20px;
    border-bottom: 1px solid var(--bg-gray);
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    background: var(--bg-white);
    position: relative;
}

.chat-item:hover {
    background: var(--bg-gray);
    transform: translateX(-5px);
    box-shadow: var(--shadow-sm);
}

.chat-item.active {
    background: linear-gradient(90deg, rgba(102, 126, 234, 0.1) 0%, var(--bg-white) 100%);
    border-left: 4px solid #667eea;
    box-shadow: inset 0 0 10px rgba(102, 126, 234, 0.1);
}

/* الصورة الرمزية الجميلة */
.chat-avatar {
    position: relative;
    width: 55px;
    height: 55px;
    margin-left: 15px;
    flex-shrink: 0;
}

.chat-avatar img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid var(--bg-white);
    box-shadow: var(--shadow-sm);
}

.avatar-text {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: var(--primary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-white);
    font-weight: bold;
    font-size: 1.2rem;
    box-shadow: var(--shadow-sm);
}

.group-badge {
    position: absolute;
    bottom: -2px;
    right: -2px;
    background: var(--success);
    color: var(--text-white);
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    border: 2px solid var(--bg-white);
    box-shadow: var(--shadow-sm);
}

.online-indicator {
    position: absolute;
    bottom: 2px;
    right: 2px;
    width: 12px;
    height: 12px;
    background: #10b981;
    border-radius: 50%;
    border: 2px solid var(--bg-white);
    box-shadow: var(--shadow-sm);
    animation: pulse 2s infinite;
}

/* محتوى المحادثة */
.chat-content {
    flex: 1;
    min-width: 0;
}

.chat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px;
}

.chat-name {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.chat-time {
    font-size: 0.8rem;
    color: var(--text-light);
    white-space: nowrap;
}

.chat-preview {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.preview-text {
    font-size: 0.9rem;
    color: var(--text-secondary);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    flex: 1;
}

.unread-badge {
    background: var(--secondary);
    color: var(--text-white);
    border-radius: 15px;
    padding: 3px 10px;
    font-size: 0.8rem;
    font-weight: bold;
    min-width: 20px;
    text-align: center;
    margin-right: 5px;
    box-shadow: var(--shadow-sm);
}

.unread-badge.pulse {
    animation: pulse 1.5s ease-in-out infinite;
}

/* رسائل الخطأ والحالات */
.no-chats-message, .error-message {
    text-align: center;
    padding: 40px 20px;
    color: var(--text-secondary);
}

.no-chats-message i, .error-message i {
    font-size: 3rem;
    margin-bottom: 15px;
    opacity: 0.6;
}

.no-chats-message h3, .error-message h3 {
    margin: 0 0 10px 0;
    font-size: 1.2rem;
    color: var(--text-primary);
}

.error-message {
    color: #ef4444;
}

.retry-btn {
    background: var(--primary);
    color: var(--text-white);
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: var(--transition);
    margin-top: 15px;
}

.retry-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

/* تحسينات عامة */
.scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: rgba(102, 126, 234, 0.3) transparent;
}

.scrollbar-thin::-webkit-scrollbar {
    width: 6px;
}

.scrollbar-thin::-webkit-scrollbar-track {
    background: transparent;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
    background: rgba(102, 126, 234, 0.3);
    border-radius: 3px;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background: rgba(102, 126, 234, 0.5);
}

/* تأثيرات جميلة */
@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.loading {
    animation: pulse 1.5s ease-in-out infinite;
}

@keyframes slideIn {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

/* تجاوب الشاشات */
@media (max-width: 768px) {
    .chat-container {
        flex-direction: column;
    }
    
    .sidebar {
        width: 100%;
        height: auto;
        border-left: none;
        border-bottom: 1px solid var(--bg-gray);
    }
    
    .login-container {
        margin: 20px;
        padding: 30px;
    }
}
