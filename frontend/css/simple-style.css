/* 🎨 تصميم بسيط وجميل */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', Arial, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    direction: rtl;
}

/* الصفحات */
.page {
    display: none;
    min-height: 100vh;
}

.page.active {
    display: flex;
}

/* صفحة تسجيل الدخول */
.login-page {
    justify-content: center;
    align-items: center;
    padding: 20px;
}

.login-container {
    background: white;
    border-radius: 15px;
    padding: 40px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    width: 100%;
    max-width: 400px;
}

.login-header {
    text-align: center;
    margin-bottom: 30px;
}

.login-header h1 {
    color: #667eea;
    font-size: 2rem;
    margin-bottom: 10px;
}

.login-header p {
    color: #666;
    font-size: 1rem;
}

/* النماذج */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: #333;
    font-weight: 600;
}

.form-group input {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-group input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* الأزرار */
.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn-primary {
    background: #667eea;
    color: white;
    width: 100%;
}

.btn-primary:hover {
    background: #5a6fd8;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

.btn-secondary {
    background: #f8f9fa;
    color: #667eea;
    border: 2px solid #667eea;
}

.btn-secondary:hover {
    background: #667eea;
    color: white;
}

/* صفحة الدردشة */
.chat-page {
    background: #f5f7fa;
}

.chat-container {
    display: flex;
    height: 100vh;
    max-width: 1200px;
    margin: 0 auto;
    background: white;
    box-shadow: 0 0 20px rgba(0,0,0,0.1);
}

/* الشريط الجانبي */
.sidebar {
    width: 350px;
    background: white;
    border-left: 1px solid #e1e5e9;
    display: flex;
    flex-direction: column;
}

.sidebar-header {
    padding: 20px;
    background: #667eea;
    color: white;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.sidebar-header h3 {
    font-size: 1.2rem;
    font-weight: 600;
}

.header-actions {
    display: flex;
    gap: 10px;
}

.new-chat-btn, .new-group-btn {
    width: 35px;
    height: 35px;
    border: none;
    border-radius: 50%;
    background: rgba(255,255,255,0.2);
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.new-chat-btn:hover, .new-group-btn:hover {
    background: rgba(255,255,255,0.3);
    transform: scale(1.1);
}

/* البحث */
.chat-search {
    padding: 15px;
    background: #f8f9fa;
    border-bottom: 1px solid #e1e5e9;
}

.search-input {
    position: relative;
}

.search-input input {
    width: 100%;
    padding: 10px 40px 10px 15px;
    border: 1px solid #e1e5e9;
    border-radius: 20px;
    font-size: 0.9rem;
    background: white;
}

.search-input i {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #999;
}

/* قائمة المحادثات */
.chat-list {
    flex: 1;
    overflow-y: auto;
}

.chat-item {
    padding: 15px 20px;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 15px;
}

.chat-item:hover {
    background: #f8f9fa;
}

.chat-item.active {
    background: #e3f2fd;
    border-left: 4px solid #667eea;
}

/* الصورة الرمزية */
.chat-avatar {
    position: relative;
    width: 50px;
    height: 50px;
    flex-shrink: 0;
}

.chat-avatar img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
}

.avatar-text {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: #667eea;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 1.2rem;
}

.online-indicator {
    position: absolute;
    bottom: 2px;
    right: 2px;
    width: 12px;
    height: 12px;
    background: #4caf50;
    border-radius: 50%;
    border: 2px solid white;
}

/* محتوى المحادثة */
.chat-content {
    flex: 1;
    min-width: 0;
}

.chat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px;
}

.chat-name {
    margin: 0;
    font-size: 1rem;
    font-weight: 600;
    color: #333;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.chat-time {
    font-size: 0.8rem;
    color: #999;
    white-space: nowrap;
}

.chat-preview {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.preview-text {
    font-size: 0.9rem;
    color: #666;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    flex: 1;
}

.unread-badge {
    background: #667eea;
    color: white;
    border-radius: 12px;
    padding: 2px 8px;
    font-size: 0.8rem;
    font-weight: bold;
    min-width: 18px;
    text-align: center;
}

/* رسائل الحالة */
.no-chats-message, .error-message {
    text-align: center;
    padding: 40px 20px;
    color: #666;
}

.no-chats-message i, .error-message i {
    font-size: 3rem;
    margin-bottom: 15px;
    opacity: 0.5;
}

.no-chats-message h3, .error-message h3 {
    margin: 0 0 10px 0;
    font-size: 1.2rem;
    color: #333;
}

.error-message {
    color: #e74c3c;
}

.retry-btn {
    background: #667eea;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.9rem;
    margin-top: 15px;
    transition: all 0.3s ease;
}

.retry-btn:hover {
    background: #5a6fd8;
    transform: translateY(-2px);
}

/* التجاوب */
@media (max-width: 768px) {
    .chat-container {
        flex-direction: column;
    }
    
    .sidebar {
        width: 100%;
        height: auto;
        border-left: none;
        border-bottom: 1px solid #e1e5e9;
    }
    
    .login-container {
        margin: 20px;
        padding: 30px;
    }
}

/* تأثيرات جميلة */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.fade-in {
    animation: fadeIn 0.3s ease-out;
}

/* شريط التمرير */
.chat-list::-webkit-scrollbar {
    width: 6px;
}

.chat-list::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.chat-list::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.chat-list::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* الإشعارات البسيطة */
.notification {
    position: fixed;
    top: 20px;
    left: 20px;
    background: white;
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    padding: 15px 20px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    z-index: 1000;
    display: flex;
    align-items: center;
    gap: 10px;
    max-width: 300px;
    animation: slideIn 0.3s ease;
}

.notification.success {
    border-left: 4px solid #4caf50;
    background: #f8fff8;
}

.notification.error {
    border-left: 4px solid #f44336;
    background: #fff8f8;
}

.notification.info {
    border-left: 4px solid #2196f3;
    background: #f8f9ff;
}

.notification button {
    background: none;
    border: none;
    font-size: 1.2rem;
    cursor: pointer;
    color: #999;
    margin-right: auto;
}

@keyframes slideIn {
    from {
        transform: translateX(-100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}
