<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الأزرار - تطبيق الدردشة</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #f0f0f0;
            padding: 20px;
            direction: rtl;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            box-sizing: border-box;
        }
        button {
            padding: 12px 20px;
            margin: 5px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .debug {
            margin-top: 20px;
            padding: 10px;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .tab-btn {
            padding: 10px 20px;
            margin: 5px;
            background: #6c757d;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        .tab-btn.active {
            background: #007bff;
        }
        .auth-form {
            display: none;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            margin-top: 10px;
        }
        .auth-form.active {
            display: block;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 اختبار أزرار تطبيق الدردشة</h1>
        
        <!-- اختبار التبويبات -->
        <div class="test-section">
            <h3>اختبار التبويبات</h3>
            <div class="login-tabs">
                <button class="tab-btn active" data-tab="login">تسجيل الدخول</button>
                <button class="tab-btn" data-tab="register">إنشاء حساب</button>
            </div>
            
            <!-- نموذج تسجيل الدخول -->
            <div id="loginForm" class="auth-form active">
                <h4>تسجيل الدخول</h4>
                <div class="form-group">
                    <label>اسم المستخدم:</label>
                    <input type="text" id="simpleUsername" value="user123" placeholder="أدخل اسم المستخدم">
                </div>
                <div class="form-group">
                    <label>كلمة المرور:</label>
                    <input type="password" id="simplePassword" value="user123" placeholder="أدخل كلمة المرور">
                </div>
                <button id="simpleLoginBtn">دخول</button>
                <div id="loginMessage" style="margin-top: 10px; padding: 10px;"></div>
            </div>
            
            <!-- نموذج التسجيل -->
            <div id="registerForm" class="auth-form">
                <h4>إنشاء حساب جديد</h4>
                <div class="form-group">
                    <label>اسم المستخدم:</label>
                    <input type="text" id="registerUsername" placeholder="أدخل اسم المستخدم">
                </div>
                <div class="form-group">
                    <label>البريد الإلكتروني:</label>
                    <input type="email" id="registerEmail" placeholder="أدخل بريدك الإلكتروني">
                </div>
                <div class="form-group">
                    <label>كلمة المرور:</label>
                    <input type="password" id="registerPassword" placeholder="أدخل كلمة المرور">
                </div>
                <div class="form-group">
                    <label>تأكيد كلمة المرور:</label>
                    <input type="password" id="confirmPassword" placeholder="أعد إدخال كلمة المرور">
                </div>
                <button id="registerBtn">إنشاء حساب</button>
                <div id="registerMessage" style="margin-top: 10px; padding: 10px;"></div>
            </div>
        </div>
        
        <!-- اختبار الأزرار المباشر -->
        <div class="test-section">
            <h3>اختبار الأزرار المباشر</h3>
            <button onclick="testSimpleLogin()">اختبار تسجيل الدخول</button>
            <button onclick="testRegister()">اختبار إنشاء الحساب</button>
            <button onclick="testTabs()">اختبار التبويبات</button>
            <button onclick="clearDebug()">مسح السجل</button>
        </div>
        
        <div id="debug" class="debug"></div>
    </div>

    <script>
        const debugDiv = document.getElementById('debug');
        
        function log(message) {
            console.log(message);
            debugDiv.textContent += new Date().toLocaleTimeString() + ': ' + message + '\n';
            debugDiv.scrollTop = debugDiv.scrollHeight;
        }
        
        function clearDebug() {
            debugDiv.textContent = '';
        }
        
        // إعداد التبويبات
        function setupTabs() {
            const tabBtns = document.querySelectorAll('.tab-btn');
            const authForms = document.querySelectorAll('.auth-form');
            
            log('🔧 إعداد التبويبات: ' + tabBtns.length + ' أزرار موجودة');
            
            tabBtns.forEach((btn, index) => {
                log(`📋 تبويب ${index}: ${btn.dataset.tab}`);
                btn.addEventListener('click', function(e) {
                    e.preventDefault();
                    const targetTab = this.dataset.tab;
                    
                    log('🔄 التبديل إلى تبويب: ' + targetTab);
                    
                    // إزالة الفئة النشطة من جميع التبويبات والنماذج
                    tabBtns.forEach(b => b.classList.remove('active'));
                    authForms.forEach(f => f.classList.remove('active'));
                    
                    // إضافة الفئة النشطة للتبويب والنموذج المحدد
                    this.classList.add('active');
                    const targetForm = document.getElementById(targetTab + 'Form');
                    if (targetForm) {
                        targetForm.classList.add('active');
                        log('✅ تم التبديل بنجاح إلى: ' + targetTab);
                    } else {
                        log('❌ النموذج غير موجود: ' + targetTab + 'Form');
                    }
                });
            });
        }
        
        // اختبار تسجيل الدخول
        function testSimpleLogin() {
            log('🔐 اختبار تسجيل الدخول');
            const username = document.getElementById('simpleUsername').value;
            const password = document.getElementById('simplePassword').value;
            log('البيانات: ' + username + ' / ' + password);
            
            if (username && password) {
                log('✅ البيانات صحيحة، يمكن المتابعة');
            } else {
                log('❌ البيانات ناقصة');
            }
        }
        
        // اختبار التسجيل
        function testRegister() {
            log('📝 اختبار إنشاء الحساب');
            const username = document.getElementById('registerUsername').value;
            const email = document.getElementById('registerEmail').value;
            const password = document.getElementById('registerPassword').value;
            const confirmPassword = document.getElementById('confirmPassword').value;
            
            log('البيانات: ' + username + ' / ' + email + ' / ' + password + ' / ' + confirmPassword);
            
            if (username && email && password && confirmPassword) {
                if (password === confirmPassword) {
                    log('✅ البيانات صحيحة، يمكن المتابعة');
                } else {
                    log('❌ كلمات المرور غير متطابقة');
                }
            } else {
                log('❌ البيانات ناقصة');
            }
        }
        
        // اختبار التبويبات
        function testTabs() {
            log('📋 اختبار التبويبات');
            const tabBtns = document.querySelectorAll('.tab-btn');
            const authForms = document.querySelectorAll('.auth-form');
            
            log('عدد أزرار التبويبات: ' + tabBtns.length);
            log('عدد النماذج: ' + authForms.length);
            
            tabBtns.forEach((btn, index) => {
                log(`تبويب ${index}: ${btn.textContent} - data-tab: ${btn.dataset.tab}`);
            });
        }
        
        // إعداد الأزرار
        function setupButtons() {
            const simpleLoginBtn = document.getElementById('simpleLoginBtn');
            const registerBtn = document.getElementById('registerBtn');
            
            if (simpleLoginBtn) {
                simpleLoginBtn.addEventListener('click', function() {
                    log('🔐 تم النقر على زر تسجيل الدخول');
                    testSimpleLogin();
                });
                log('✅ زر تسجيل الدخول جاهز');
            } else {
                log('❌ زر تسجيل الدخول غير موجود');
            }
            
            if (registerBtn) {
                registerBtn.addEventListener('click', function() {
                    log('📝 تم النقر على زر إنشاء الحساب');
                    testRegister();
                });
                log('✅ زر إنشاء الحساب جاهز');
            } else {
                log('❌ زر إنشاء الحساب غير موجود');
            }
        }
        
        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            log('🚀 تحميل صفحة الاختبار');
            setupTabs();
            setupButtons();
            log('✅ الصفحة جاهزة للاختبار');
        });
    </script>
</body>
</html>
