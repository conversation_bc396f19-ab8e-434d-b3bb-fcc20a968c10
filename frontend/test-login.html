<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تسجيل الدخول</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #f0f0f0;
            padding: 20px;
        }
        .container {
            max-width: 400px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            box-sizing: border-box;
        }
        button {
            width: 100%;
            padding: 12px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .message {
            margin-top: 15px;
            padding: 10px;
            border-radius: 5px;
            text-align: center;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .debug {
            margin-top: 20px;
            padding: 10px;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 اختبار تسجيل الدخول</h1>
        
        <div class="form-group">
            <label>اسم المستخدم:</label>
            <input type="text" id="username" value="user123" placeholder="أدخل اسم المستخدم">
        </div>
        
        <div class="form-group">
            <label>كلمة المرور:</label>
            <input type="password" id="password" value="user123" placeholder="أدخل كلمة المرور">
        </div>
        
        <button id="loginBtn" onclick="testLogin()">اختبار تسجيل الدخول</button>
        
        <div id="message" style="display: none;"></div>
        <div id="debug" class="debug"></div>
    </div>

    <script>
        const debugDiv = document.getElementById('debug');
        
        function log(message) {
            console.log(message);
            debugDiv.textContent += new Date().toLocaleTimeString() + ': ' + message + '\n';
        }
        
        function showMessage(text, type) {
            const messageDiv = document.getElementById('message');
            messageDiv.innerHTML = text;
            messageDiv.className = 'message ' + type;
            messageDiv.style.display = 'block';
        }
        
        async function testLogin() {
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value.trim();
            const loginBtn = document.getElementById('loginBtn');
            
            log('🔐 بدء اختبار تسجيل الدخول');
            log('اسم المستخدم: ' + username);
            
            if (!username || !password) {
                showMessage('يرجى ملء جميع الحقول', 'error');
                log('❌ حقول فارغة');
                return;
            }
            
            // تعطيل الزر
            loginBtn.disabled = true;
            loginBtn.innerHTML = 'جاري الاختبار...';
            showMessage('جاري اختبار تسجيل الدخول...', 'info');
            
            try {
                log('📡 إرسال طلب إلى: http://192.168.10.88:8000/api/v1/auth/login');
                
                const response = await fetch('http://192.168.10.88:8000/api/v1/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: username,
                        password: password
                    })
                });
                
                log('📡 حالة الاستجابة: ' + response.status);
                log('📡 نوع المحتوى: ' + response.headers.get('content-type'));
                
                if (response.ok) {
                    const data = await response.json();
                    log('✅ تسجيل الدخول ناجح!');
                    log('🔑 التوكن: ' + data.access_token.substring(0, 50) + '...');
                    
                    showMessage('✅ تم تسجيل الدخول بنجاح!', 'success');
                    
                    // حفظ التوكن
                    localStorage.setItem('authToken', data.access_token);
                    log('💾 تم حفظ التوكن في localStorage');
                    
                } else {
                    const errorData = await response.json().catch(() => ({}));
                    log('❌ فشل تسجيل الدخول: ' + response.status);
                    log('❌ تفاصيل الخطأ: ' + JSON.stringify(errorData));
                    showMessage('❌ فشل تسجيل الدخول: ' + (errorData.detail || 'خطأ غير معروف'), 'error');
                }
                
            } catch (error) {
                log('💥 خطأ في الشبكة: ' + error.message);
                showMessage('💥 خطأ في الاتصال: ' + error.message, 'error');
            } finally {
                loginBtn.disabled = false;
                loginBtn.innerHTML = 'اختبار تسجيل الدخول';
            }
        }
        
        // مستمع Enter
        document.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                testLogin();
            }
        });
        
        log('🚀 صفحة اختبار تسجيل الدخول جاهزة');
    </script>
</body>
</html>
