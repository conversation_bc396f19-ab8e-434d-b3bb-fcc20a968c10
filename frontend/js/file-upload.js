// مكون رفع الملفات
class FileUploader {
    constructor() {
        this.maxFileSize = 50 * 1024 * 1024; // 50MB
        this.allowedTypes = {
            image: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
            video: ['video/mp4', 'video/webm', 'video/ogg'],
            audio: ['audio/mp3', 'audio/wav', 'audio/ogg', 'audio/m4a'],
            document: [
                'application/pdf',
                'application/msword',
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                'application/vnd.ms-excel',
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                'application/vnd.ms-powerpoint',
                'application/vnd.openxmlformats-officedocument.presentationml.presentation',
                'text/plain'
            ]
        };
        this.init();
    }

    init() {
        this.createFileInput();
        this.createDropZone();
        this.setupEventListeners();
    }

    createFileInput() {
        const fileInput = document.createElement('input');
        fileInput.type = 'file';
        fileInput.id = 'fileInput';
        fileInput.style.display = 'none';
        fileInput.multiple = true;
        document.body.appendChild(fileInput);
    }

    createDropZone() {
        const dropZone = document.createElement('div');
        dropZone.id = 'dropZone';
        dropZone.className = 'drop-zone';
        dropZone.style.display = 'none';
        dropZone.innerHTML = `
            <div class="drop-zone-content">
                <i class="fas fa-cloud-upload-alt"></i>
                <h3>اسحب الملفات هنا أو انقر للاختيار</h3>
                <p>الحد الأقصى: 50MB لكل ملف</p>
                <button class="drop-zone-btn">اختيار الملفات</button>
            </div>
        `;
        document.body.appendChild(dropZone);
    }

    setupEventListeners() {
        const fileInput = document.getElementById('fileInput');
        const dropZone = document.getElementById('dropZone');

        // معالجة اختيار الملفات
        fileInput.addEventListener('change', (e) => {
            this.handleFiles(e.target.files);
        });

        // معالجة السحب والإفلات
        dropZone.addEventListener('dragover', (e) => {
            e.preventDefault();
            dropZone.classList.add('drag-over');
        });

        dropZone.addEventListener('dragleave', (e) => {
            e.preventDefault();
            dropZone.classList.remove('drag-over');
        });

        dropZone.addEventListener('drop', (e) => {
            e.preventDefault();
            dropZone.classList.remove('drag-over');
            this.handleFiles(e.dataTransfer.files);
            this.hideDropZone();
        });

        // النقر لاختيار الملفات
        dropZone.querySelector('.drop-zone-btn').addEventListener('click', () => {
            fileInput.click();
        });

        // إغلاق منطقة الإفلات عند النقر خارجها
        dropZone.addEventListener('click', (e) => {
            if (e.target === dropZone) {
                this.hideDropZone();
            }
        });
    }

    showDropZone() {
        document.getElementById('dropZone').style.display = 'flex';
    }

    hideDropZone() {
        document.getElementById('dropZone').style.display = 'none';
    }

    openFileDialog() {
        document.getElementById('fileInput').click();
    }

    handleFiles(files) {
        Array.from(files).forEach(file => {
            if (this.validateFile(file)) {
                this.uploadFile(file);
            }
        });
    }

    validateFile(file) {
        // التحقق من حجم الملف
        if (file.size > this.maxFileSize) {
            showNotification(`الملف "${file.name}" كبير جداً. الحد الأقصى 50MB`, 'error');
            return false;
        }

        // التحقق من نوع الملف
        const fileType = this.getFileType(file.type);
        if (!fileType) {
            showNotification(`نوع الملف "${file.name}" غير مدعوم`, 'error');
            return false;
        }

        return true;
    }

    getFileType(mimeType) {
        for (const [type, mimes] of Object.entries(this.allowedTypes)) {
            if (mimes.includes(mimeType)) {
                return type;
            }
        }
        return null;
    }

    async uploadFile(file) {
        const fileType = this.getFileType(file.type);
        
        // إنشاء معاينة للملف
        const preview = this.createFilePreview(file, fileType);
        this.showFilePreview(preview);

        try {
            // رفع الملف
            const formData = new FormData();
            formData.append('file', file);
            formData.append('type', fileType);

            const response = await fetch('/api/v1/files/upload', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${authToken}`
                },
                body: formData
            });

            if (response.ok) {
                const result = await response.json();
                
                // تحديث معاينة الملف بالرابط
                preview.dataset.fileUrl = result.file_url;
                preview.dataset.fileId = result.file_id;
                preview.classList.add('uploaded');
                
                // إزالة مؤشر التحميل
                preview.querySelector('.upload-progress').style.display = 'none';
                preview.querySelector('.file-actions').style.display = 'flex';
                
                showNotification(`تم رفع الملف "${file.name}" بنجاح`, 'success');
            } else {
                throw new Error('فشل في رفع الملف');
            }
        } catch (error) {
            console.error('Upload error:', error);
            showNotification(`فشل في رفع الملف "${file.name}"`, 'error');
            preview.remove();
        }
    }

    createFilePreview(file, fileType) {
        const preview = document.createElement('div');
        preview.className = 'file-preview';
        preview.dataset.fileName = file.name;
        preview.dataset.fileType = fileType;
        preview.dataset.fileSize = file.size;

        let content = '';
        
        if (fileType === 'image') {
            const reader = new FileReader();
            reader.onload = (e) => {
                preview.querySelector('.file-thumbnail').innerHTML = `<img src="${e.target.result}" alt="${file.name}">`;
            };
            reader.readAsDataURL(file);
            content = '<div class="file-thumbnail"><div class="loading-placeholder"></div></div>';
        } else {
            const iconMap = {
                video: 'fa-video',
                audio: 'fa-music',
                document: 'fa-file-alt'
            };
            content = `<div class="file-thumbnail"><i class="fas ${iconMap[fileType] || 'fa-file'}"></i></div>`;
        }

        preview.innerHTML = `
            ${content}
            <div class="file-info">
                <div class="file-name">${file.name}</div>
                <div class="file-size">${this.formatFileSize(file.size)}</div>
            </div>
            <div class="upload-progress">
                <div class="progress-bar">
                    <div class="progress-fill"></div>
                </div>
                <span class="progress-text">جاري الرفع...</span>
            </div>
            <div class="file-actions" style="display: none;">
                <button class="file-action-btn send-btn" title="إرسال">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <button class="file-action-btn remove-btn" title="حذف">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        `;

        // إضافة مستمعي الأحداث
        preview.querySelector('.send-btn').addEventListener('click', () => {
            this.sendFile(preview);
        });

        preview.querySelector('.remove-btn').addEventListener('click', () => {
            preview.remove();
        });

        return preview;
    }

    showFilePreview(preview) {
        let container = document.getElementById('filePreviewContainer');
        if (!container) {
            container = document.createElement('div');
            container.id = 'filePreviewContainer';
            container.className = 'file-preview-container';
            
            // إدراج الحاوية قبل منطقة إدخال الرسائل
            const messageInputContainer = document.querySelector('.message-input-container');
            messageInputContainer.parentNode.insertBefore(container, messageInputContainer);
        }
        
        container.appendChild(preview);
        container.style.display = 'block';
    }

    sendFile(preview) {
        const fileUrl = preview.dataset.fileUrl;
        const fileName = preview.dataset.fileName;
        const fileType = preview.dataset.fileType;
        const fileSize = preview.dataset.fileSize;

        if (!fileUrl || !currentChatId) {
            showNotification('لا يمكن إرسال الملف', 'error');
            return;
        }

        // إرسال رسالة الملف
        const messageData = {
            type: 'file_message',
            data: {
                content: fileName,
                content_type: fileType,
                file_url: fileUrl,
                file_name: fileName,
                file_size: parseInt(fileSize),
                recipient_id: currentChatId
            }
        };

        if (websocket && websocket.readyState === WebSocket.OPEN) {
            websocket.send(JSON.stringify(messageData));
            preview.remove();
            
            // إخفاء الحاوية إذا لم تعد تحتوي على ملفات
            const container = document.getElementById('filePreviewContainer');
            if (container && container.children.length === 0) {
                container.style.display = 'none';
            }
        }
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
}

// إنشاء رافع الملفات العام
let fileUploader = null;

// دالة لإظهار منطقة رفع الملفات
function showFileUploader() {
    if (!fileUploader) {
        fileUploader = new FileUploader();
    }
    fileUploader.showDropZone();
}

// دالة لفتح حوار اختيار الملفات
function openFileDialog() {
    if (!fileUploader) {
        fileUploader = new FileUploader();
    }
    fileUploader.openFileDialog();
}

// تصدير للاستخدام في ملفات أخرى
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { FileUploader, showFileUploader, openFileDialog };
}
