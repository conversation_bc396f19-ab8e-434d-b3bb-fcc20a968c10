// إعدادات التطبيق
const API_BASE_URL = `http://${window.location.hostname}:8000/api/v1`;
const WS_URL = `ws://${window.location.hostname}:8000/ws`;

// متغيرات عامة
let currentUser = null;
let authToken = null;
let websocket = null;
let currentChatId = null;
let currentChatType = 'direct'; // 'direct' أو 'group'
let currentLoadedConversation = null; // لمنع إعادة التحميل
let currentPage = 1; // للـ pagination
let isLoadingMessages = false; // لمنع التحميل المتعدد
let hasMoreMessages = true; // هل توجد رسائل أكثر
let isTyping = false;
let typingTimeout = null;

// عناصر DOM - سيتم تحديدها بعد تحميل DOM
let loginPage, chatPage, loginForm, registerForm, messageForm;
let messageInput, messagesContainer, chatList, welcomeScreen, chatWindow;
let newChatModal, usersList, notificationsContainer;

// تهيئة عناصر DOM
function initializeDOMElements() {
    loginPage = document.getElementById('loginPage');
    chatPage = document.getElementById('chatPage');
    loginForm = document.getElementById('loginForm');
    registerForm = document.getElementById('registerForm');
    messageForm = document.getElementById('messageForm');
    messageInput = document.getElementById('messageInput');
    messagesContainer = document.getElementById('messagesContainer');
    chatList = document.getElementById('chatList');
    welcomeScreen = document.getElementById('welcomeScreen');
    chatWindow = document.getElementById('chatWindow');
    newChatModal = document.getElementById('newChatModal');
    usersList = document.getElementById('usersList');
    notificationsContainer = document.getElementById('notifications');

    console.log('🔧 DOM Elements initialized:');
    console.log('- loginForm:', !!loginForm);
    console.log('- registerForm:', !!registerForm);
    console.log('- Tab buttons:', document.querySelectorAll('.tab-btn').length);
    console.log('- chatList:', !!chatList);
    console.log('- chatPage:', !!chatPage);
    console.log('- loginPage:', !!loginPage);
}

// بدء التطبيق
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM Content Loaded');

    initializeDOMElements();
    initializeApp();
    setupEventListeners();
    initializeProfile();
    initializeEmojiSystem();
    initializeFileSystem();
    initializeVoiceSystem();
    initializeAdvancedSearch();
    initializeThemeSystem();
    initializeTypingIndicator();
    checkAuthStatus();

    // تهيئة نظام الإشعارات
    initializeNotifications();

    // تهيئة محسن الأداء
    initializePerformanceOptimizer();

    // إضافة تأثيرات التصميم
    initializeDesignEffects();
});

// تهيئة التطبيق
function initializeApp() {
    console.log('🚀 Initializing app...');

    // التحقق من وجود token محفوظ
    const savedToken = localStorage.getItem('authToken');
    if (savedToken) {
        console.log('🔑 Found saved token, validating...');
        authToken = savedToken;
        validateToken().then(isValid => {
            if (isValid) {
                console.log('✅ Token is valid, loading chat page');
                showChatPage();
                loadChats();
            } else {
                console.log('❌ Token is invalid, showing login');
                showLoginPage();
            }
        });
    } else {
        console.log('❌ No token found, showing login');
        showLoginPage();
    }

    // إعداد التبويبات
    setupTabs();

    // إعداد كلمات المرور
    setupPasswordToggles();

    // إعداد حالة الاتصال
    createConnectionStatus();
}

// إعداد مستمعي الأحداث
function setupEventListeners() {
    // زر تسجيل الدخول البسيط
    const simpleLoginBtn = document.getElementById('simpleLoginBtn');

    if (simpleLoginBtn) {
        simpleLoginBtn.addEventListener('click', simpleLogin);
        console.log('✅ Simple login button ready');
    }

    // مستمع Enter للحقول
    const simpleUsername = document.getElementById('simpleUsername');
    const simplePassword = document.getElementById('simplePassword');

    if (simpleUsername) {
        simpleUsername.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                simpleLogin();
            }
        });
    }

    if (simplePassword) {
        simplePassword.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                simpleLogin();
            }
        });
    }
    
    // نموذج الرسائل
    messageForm.addEventListener('submit', handleSendMessage);
    messageInput.addEventListener('input', handleTyping);
    
    // أزرار الواجهة
    document.getElementById('logoutBtn').addEventListener('click', handleLogout);
    document.getElementById('newChatBtn').addEventListener('click', showNewChatModal);
    document.getElementById('closeModalBtn').addEventListener('click', hideNewChatModal);
    
    // البحث عن المستخدمين
    document.getElementById('userSearchInput').addEventListener('input', searchUsers);

    // أزرار الميزات الجديدة
    document.getElementById('emojiBtn').addEventListener('click', function() {
        const messageInput = document.getElementById('messageInput');
        showEmojiPicker(messageInput, this);
    });

    document.getElementById('fileBtn').addEventListener('click', function() {
        openFileDialog();
    });

    document.getElementById('voiceBtn').addEventListener('click', function() {
        // سيتم تطوير الرسائل الصوتية لاحقاً
        showNotification('الرسائل الصوتية قيد التطوير', 'info');
    });

    // أزرار المجموعات
    document.getElementById('newGroupBtn').addEventListener('click', showNewGroupModal);
    document.getElementById('closeGroupModalBtn').addEventListener('click', hideNewGroupModal);
    document.getElementById('cancelGroupBtn').addEventListener('click', hideNewGroupModal);
    document.getElementById('createGroupForm').addEventListener('submit', createGroup);
    document.getElementById('memberSearchInput').addEventListener('input', searchMembers);

    // أزرار البحث والمزيد
    document.getElementById('searchBtn').addEventListener('click', toggleSearchBar);
    document.getElementById('closeSearchBtn').addEventListener('click', hideSearchBar);
    document.getElementById('messageSearchInput').addEventListener('input', searchMessages);
    document.getElementById('callBtn').addEventListener('click', () => showNotification('المكالمات الصوتية قيد التطوير', 'info'));
    document.getElementById('videoBtn').addEventListener('click', () => showNotification('المكالمات المرئية قيد التطوير', 'info'));
    document.getElementById('profileBtn').addEventListener('click', showProfileModal);
    document.getElementById('moreBtn').addEventListener('click', showMoreOptions);

    // أزرار الملف الشخصي
    document.getElementById('closeProfileModalBtn').addEventListener('click', hideProfileModal);
    document.getElementById('cancelProfileBtn').addEventListener('click', hideProfileModal);
    document.getElementById('profileForm').addEventListener('submit', updateProfile);
    document.getElementById('changeAvatarBtn').addEventListener('click', () => document.getElementById('avatarInput').click());
    document.getElementById('avatarInput').addEventListener('change', handleAvatarChange);

    // تبويبات الإعدادات
    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.addEventListener('click', (e) => switchTab(e.target.dataset.tab));
    });

    // إعدادات التطبيق
    document.getElementById('darkModeToggle').addEventListener('change', toggleDarkMode);
    document.getElementById('fontSizeSelect').addEventListener('change', changeFontSize);
    document.getElementById('enterToSendToggle').addEventListener('change', toggleEnterToSend);
    document.getElementById('desktopNotificationsToggle').addEventListener('change', toggleDesktopNotifications);

    // إعدادات الإشعارات المتقدمة
    document.getElementById('notificationSoundSelect').addEventListener('change', changeNotificationSound);
    document.getElementById('volumeSlider').addEventListener('input', changeVolume);
    document.getElementById('doNotDisturbToggle').addEventListener('change', toggleDoNotDisturb);
    document.getElementById('quietHoursToggle').addEventListener('change', toggleQuietHours);
    document.getElementById('testNotificationBtn').addEventListener('click', testNotification);
    
    // إغلاق النافذة المنبثقة عند النقر خارجها
    newChatModal.addEventListener('click', function(e) {
        if (e.target === newChatModal) {
            hideNewChatModal();
        }
    });
}

// إعداد التبويبات
function setupTabs() {
    const tabBtns = document.querySelectorAll('.tab-btn');
    const authForms = document.querySelectorAll('.auth-form');

    console.log('🔧 Setting up tabs:', tabBtns.length, 'buttons found');

    tabBtns.forEach((btn, index) => {
        console.log(`📋 Tab ${index}:`, btn.dataset.tab);
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            const targetTab = this.dataset.tab;

            console.log('🔄 Switching to tab:', targetTab);

            // إزالة الفئة النشطة من جميع التبويبات والنماذج
            tabBtns.forEach(b => b.classList.remove('active'));
            authForms.forEach(f => f.classList.remove('active'));

            // إضافة الفئة النشطة للتبويب والنموذج المحدد
            this.classList.add('active');
            const targetForm = document.getElementById(targetTab + 'Form');
            if (targetForm) {
                targetForm.classList.add('active');
                console.log('✅ Tab switched successfully to:', targetTab);
            } else {
                console.error('❌ Form not found:', targetTab + 'Form');
            }
        });
    });
}

// إعداد إظهار/إخفاء كلمة المرور
function setupPasswordToggles() {
    const toggleBtns = document.querySelectorAll('.toggle-password');
    
    toggleBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const input = this.parentElement.querySelector('input');
            const icon = this.querySelector('i');
            
            if (input.type === 'password') {
                input.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                input.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        });
    });
}

// التحقق من حالة المصادقة
async function checkAuthStatus() {
    const token = localStorage.getItem('authToken');
    const user = localStorage.getItem('currentUser');

    if (token && user) {
        authToken = token;
        currentUser = JSON.parse(user);
        showChatPage();
        connectWebSocket();

        // استخدام التحقق المحسن
        await loadChatsWithValidation();
    } else {
        showLoginPage();
    }
}

// عرض صفحة تسجيل الدخول
function showLoginPage() {
    loginPage.classList.add('active');
    chatPage.classList.remove('active');
}

// عرض صفحة الدردشة مع تأثيرات جميلة
function showChatPage() {
    // إضافة تأثير انتقال جميل
    loginPage.style.transition = 'all 0.5s cubic-bezier(0.4, 0, 0.2, 1)';
    loginPage.style.transform = 'scale(0.9)';
    loginPage.style.opacity = '0';

    setTimeout(() => {
        loginPage.classList.remove('active');
        chatPage.classList.add('active');

        // تأثير ظهور صفحة الدردشة
        chatPage.style.opacity = '0';
        chatPage.style.transform = 'scale(1.1)';

        setTimeout(() => {
            chatPage.style.transition = 'all 0.5s cubic-bezier(0.4, 0, 0.2, 1)';
            chatPage.style.opacity = '1';
            chatPage.style.transform = 'scale(1)';
        }, 50);

        if (currentUser) {
            document.getElementById('currentUserName').textContent = currentUser.username;
        }
    }, 300);
}

// دالة تسجيل الدخول البسيطة
async function simpleLogin() {
    console.log('🔐 Simple login started');

    const username = document.getElementById('simpleUsername').value.trim();
    const password = document.getElementById('simplePassword').value.trim();
    const messageDiv = document.getElementById('loginMessage');
    const loginBtn = document.getElementById('simpleLoginBtn');

    console.log('📝 Login attempt:', { username: username });

    if (!username || !password) {
        messageDiv.innerHTML = '<span style="color: red;">يرجى ملء جميع الحقول</span>';
        return;
    }

    // تعطيل الزر أثناء التحميل
    loginBtn.disabled = true;
    loginBtn.innerHTML = 'جاري تسجيل الدخول...';
    messageDiv.innerHTML = '<span style="color: blue;">جاري تسجيل الدخول...</span>';

    try {
        // إرسال طلب تسجيل الدخول
        const response = await fetch('http://192.168.10.88:8000/api/v1/auth/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                username: username,
                password: password
            })
        });

        console.log('📡 Response status:', response.status);

        if (response.ok) {
            const data = await response.json();
            console.log('✅ Login successful');

            // حفظ التوكن
            authToken = data.access_token;
            localStorage.setItem('authToken', authToken);

            // الحصول على بيانات المستخدم
            const userResponse = await fetch('http://192.168.10.88:8000/api/v1/auth/me', {
                headers: {
                    'Authorization': `Bearer ${authToken}`
                }
            });

            if (userResponse.ok) {
                currentUser = await userResponse.json();
                console.log('👤 User data:', currentUser);

                messageDiv.innerHTML = '<span style="color: green;">تم تسجيل الدخول بنجاح!</span>';

                // الانتقال لصفحة الدردشة
                setTimeout(() => {
                    showChatPage();
                }, 1000);
            }
        } else {
            console.error('❌ Login failed');
            messageDiv.innerHTML = '<span style="color: red;">اسم المستخدم أو كلمة المرور غير صحيحة</span>';
        }
    } catch (error) {
        console.error('💥 Login error:', error);
        messageDiv.innerHTML = '<span style="color: red;">حدث خطأ في الاتصال</span>';
    } finally {
        // إعادة تفعيل الزر
        loginBtn.disabled = false;
        loginBtn.innerHTML = 'دخول';
    }
}

// تنفيذ عملية تسجيل الدخول
async function performLogin(username, password) {
    const loginData = { username, password };

    try {
        const response = await fetch(`${API_BASE_URL}/auth/login`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(loginData)
        });
        
        if (response.ok) {
            const data = await response.json();
            authToken = data.access_token;

            console.log('✅ Login successful, token received');

            // الحصول على بيانات المستخدم
            const userResponse = await fetch(`${API_BASE_URL}/auth/me`, {
                headers: {
                    'Authorization': `Bearer ${authToken}`
                }
            });
            
            if (userResponse.ok) {
                currentUser = await userResponse.json();
                
                // حفظ البيانات في التخزين المحلي
                localStorage.setItem('authToken', authToken);
                localStorage.setItem('currentUser', JSON.stringify(currentUser));
                
                // تأثير جميل لتسجيل الدخول
                showNotification(`🎉 مرحباً بك ${currentUser.username}! تم تسجيل الدخول بنجاح`, 'success');

                // إضافة تأثير انتقال جميل
                loginPage.style.transform = 'scale(0.95)';
                loginPage.style.opacity = '0.5';

                setTimeout(() => {
                    showChatPage();
                    connectWebSocket();
                }, 300);

                // تحميل المحادثات مع تأخير قصير للتأكد من تحميل DOM
                setTimeout(async () => {
                    console.log('Loading chats after login...');
                    await loadChatsWithValidation();
                }, 500);
            }
        } else {
            console.error('❌ Login failed with status:', response.status);
            const errorData = await response.json().catch(() => ({}));
            let errorMessage = 'خطأ في تسجيل الدخول';

            if (response.status === 401) {
                errorMessage = 'اسم المستخدم أو كلمة المرور غير صحيحة';
            } else if (response.status === 422) {
                errorMessage = 'يرجى التحقق من البيانات المدخلة';
            } else if (errorData.detail) {
                errorMessage = errorData.detail;
            }

            console.error('Login error details:', errorData);
            showNotification(errorMessage, 'error');
        }
    } catch (error) {
        console.error('Login error:', error);
        if (error.name === 'TypeError' && error.message.includes('fetch')) {
            showNotification('لا يمكن الاتصال بالخادم. يرجى التحقق من الاتصال بالإنترنت.', 'error');
        } else {
            showNotification('حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.', 'error');
        }
    } finally {
        setButtonLoading(submitBtn, false);
    }

    return false;
}

// معالجة نقر زر التسجيل
async function handleRegisterClick() {
    console.log('📝 Register button clicked');

    const username = document.getElementById('registerUsername').value.trim();
    const email = document.getElementById('registerEmail').value.trim();
    const password = document.getElementById('registerPassword').value.trim();
    const confirmPassword = document.getElementById('confirmPassword').value.trim();

    if (!username || !email || !password || !confirmPassword) {
        showNotification('يرجى ملء جميع الحقول', 'error');
        return;
    }

    if (password !== confirmPassword) {
        showNotification('كلمات المرور غير متطابقة', 'error');
        return;
    }

    if (password.length < 6) {
        showNotification('كلمة المرور يجب أن تكون 6 أحرف على الأقل', 'error');
        return;
    }

    const registerBtn = document.getElementById('registerBtn');
    setButtonLoading(registerBtn, true);

    try {
        await performRegister(username, email, password);
    } catch (error) {
        console.error('Register error:', error);
        showNotification('حدث خطأ في إنشاء الحساب', 'error');
    } finally {
        setButtonLoading(registerBtn, false);
    }
}

// تنفيذ عملية التسجيل
async function performRegister(username, email, password) {
    const registerData = { username, email, password };

    try {
        const response = await fetch(`${API_BASE_URL}/auth/register`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(registerData)
        });

        if (response.ok) {
            showNotification('تم إنشاء الحساب بنجاح! يمكنك الآن تسجيل الدخول', 'success');

            // التبديل إلى تبويب تسجيل الدخول
            document.querySelector('.tab-btn[data-tab="login"]').click();

            // ملء اسم المستخدم في نموذج تسجيل الدخول
            document.getElementById('loginUsername').value = username;
        } else {
            const errorData = await response.json().catch(() => ({}));
            let errorMessage = 'خطأ في إنشاء الحساب';

            if (response.status === 400) {
                errorMessage = 'اسم المستخدم أو البريد الإلكتروني مستخدم بالفعل';
            } else if (errorData.detail) {
                errorMessage = errorData.detail;
            }

            showNotification(errorMessage, 'error');
        }
    } catch (error) {
        console.error('Register error:', error);
        showNotification('حدث خطأ في الاتصال بالخادم', 'error');
    }
}



// معالجة تسجيل الخروج
function handleLogout() {
    // قطع اتصال WebSocket
    if (websocket) {
        websocket.close();
        websocket = null;
    }
    
    // مسح البيانات المحفوظة
    localStorage.removeItem('authToken');
    localStorage.removeItem('currentUser');
    
    // إعادة تعيين المتغيرات
    currentUser = null;
    authToken = null;
    currentChatId = null;
    
    // عرض صفحة تسجيل الدخول
    showLoginPage();
    showNotification('تم تسجيل الخروج بنجاح', 'success');
}

// اتصال WebSocket
let reconnectAttempts = 0;
const maxReconnectAttempts = 5;

function connectWebSocket() {
    if (!authToken) return;

    updateConnectionStatus('connecting');

    websocket = new WebSocket(`${WS_URL}/${authToken}`);

    websocket.onopen = function() {
        console.log('WebSocket connected');
        updateConnectionStatus('connected');
        reconnectAttempts = 0; // إعادة تعيين عداد المحاولات
        showNotification('تم الاتصال بالخادم بنجاح', 'success');
    };

    websocket.onmessage = function(event) {
        try {
            const data = JSON.parse(event.data);
            handleWebSocketMessage(data);
        } catch (error) {
            console.error('Error parsing WebSocket message:', error);
        }
    };

    websocket.onclose = function(event) {
        console.log('WebSocket disconnected', event.code, event.reason);
        updateConnectionStatus('disconnected');

        // إعادة الاتصال مع backoff
        if (authToken && reconnectAttempts < maxReconnectAttempts) {
            reconnectAttempts++;
            const delay = Math.min(1000 * Math.pow(2, reconnectAttempts), 30000); // exponential backoff

            setTimeout(() => {
                if (authToken) {
                    console.log(`Attempting to reconnect... (${reconnectAttempts}/${maxReconnectAttempts})`);
                    connectWebSocket();
                }
            }, delay);
        } else if (reconnectAttempts >= maxReconnectAttempts) {
            showNotification('فشل في الاتصال بالخادم. يرجى إعادة تحميل الصفحة.', 'error');
        }
    };

    websocket.onerror = function(error) {
        console.error('WebSocket error:', error);
        updateConnectionStatus('disconnected');
    };
}

// معالجة رسائل WebSocket
function handleWebSocketMessage(data) {
    console.log('WebSocket message received:', data); // للتشخيص

    switch (data.type) {
        case 'new_message':
        case 'chat_message':
            // عرض الرسالة فوراً
            displayMessage(data.data);

            // تحديث قائمة المحادثات فوراً - مثل واتساب
            updateChatListAfterMessage(data.data);

            // إشعار للرسائل الجديدة (إذا لم تكن من المستخدم الحالي)
            if (data.data.sender_id !== currentUser?.id) {
                const sender = {
                    id: data.data.sender_id,
                    username: data.data.sender_username || data.data.sender_name || 'مستخدم',
                    avatar_url: data.data.sender_avatar
                };

                // تشغيل الإشعار فقط إذا كانت النافذة غير نشطة أو المحادثة غير مفتوحة
                if (document.hidden || (currentChatId !== data.data.sender_id && currentChatId !== data.data.room_id)) {
                    notifyNewMessage(data.data, sender);
                }
            }
            break;

        case 'message_sent':
            // تأكيد إرسال الرسالة
            console.log('Message sent confirmation:', data.data);
            // يمكن إضافة تحديث حالة الرسالة هنا
            break;

        case 'typing':
            handleTypingIndicator(data.data);
            break;

        case 'user_status':
            updateUserStatus(data.data);

            // إشعار حالة المستخدم
            if (data.data.status === 'online' && data.data.user_id !== currentUser?.id) {
                const user = {
                    id: data.data.user_id,
                    username: data.data.username,
                    avatar_url: data.data.avatar_url
                };

                if (notificationSystem) {
                    notificationSystem.notifyUserStatus(user, 'online');
                }
            }
            break;

        case 'mention':
            // إشعار الذكر في المجموعات
            if (data.data.mentioned_user_id === currentUser?.id) {
                const sender = {
                    id: data.data.sender_id,
                    username: data.data.sender_name,
                    avatar_url: data.data.sender_avatar
                };

                const group = {
                    id: data.data.group_id,
                    name: data.data.group_name,
                    avatar_url: data.data.group_avatar
                };

                notifyMention(data.data, sender, group);
            }
            break;

        case 'call_incoming':
            // إشعار مكالمة واردة
            const caller = {
                id: data.data.caller_id,
                username: data.data.caller_name,
                avatar_url: data.data.caller_avatar
            };

            notifyIncomingCall(caller, data.data.call_type);
            break;

        default:
            console.log('Unknown message type:', data.type);
    }
}

// تحديث حالة الاتصال
function updateConnectionStatus(status) {
    let statusElement = document.querySelector('.connection-status');
    
    if (!statusElement) {
        statusElement = document.createElement('div');
        statusElement.className = 'connection-status';
        document.body.appendChild(statusElement);
    }
    
    statusElement.className = `connection-status ${status}`;
    
    const indicator = '<div class="connection-indicator"></div>';
    
    switch (status) {
        case 'connected':
            statusElement.innerHTML = `${indicator} متصل`;
            break;
        case 'connecting':
            statusElement.innerHTML = `${indicator} جاري الاتصال...`;
            break;
        case 'disconnected':
            statusElement.innerHTML = `${indicator} غير متصل`;
            break;
    }
}

// إنشاء حالة الاتصال
function createConnectionStatus() {
    updateConnectionStatus('disconnected');
}

// تعيين حالة التحميل للزر
function setButtonLoading(button, loading) {
    if (loading) {
        button.classList.add('btn-loading');
        button.disabled = true;
    } else {
        button.classList.remove('btn-loading');
        button.disabled = false;
    }
}

// عرض التنبيهات
function showNotification(message, type = 'info', title = '') {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;

    const iconMap = {
        success: 'fa-check-circle',
        error: 'fa-exclamation-circle',
        warning: 'fa-exclamation-triangle',
        info: 'fa-info-circle'
    };

    notification.innerHTML = `
        <div class="notification-icon">
            <i class="fas ${iconMap[type] || iconMap.info}"></i>
        </div>
        <div class="notification-content">
            ${title ? `<div class="notification-title">${title}</div>` : ''}
            <div class="notification-message">${message}</div>
        </div>
        <button class="notification-close">
            <i class="fas fa-times"></i>
        </button>
    `;

    // إضافة مستمع إغلاق التنبيه
    notification.querySelector('.notification-close').addEventListener('click', function() {
        closeNotification(notification);
    });

    // تأثير ظهور جميل
    notification.style.transform = 'translateX(100%)';
    notification.style.opacity = '0';

    notificationsContainer.appendChild(notification);

    // تأثير انزلاق جميل
    setTimeout(() => {
        notification.style.transition = 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)';
        notification.style.transform = 'translateX(0)';
        notification.style.opacity = '1';
    }, 50);

    // إضافة صوت للتنبيهات (اختياري)
    if (type === 'success') {
        playNotificationSound('success');
    } else if (type === 'error') {
        playNotificationSound('error');
    }

    // إزالة التنبيه تلقائياً بعد 5 ثوان
    setTimeout(() => {
        if (notification.parentNode) {
            closeNotification(notification);
        }
    }, 5000);
}

// إغلاق التنبيه
function closeNotification(notification) {
    notification.style.animation = 'slideOut 0.3s ease forwards';
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 300);
}

// تشغيل صوت التنبيه (اختياري)
function playNotificationSound(type) {
    // يمكن إضافة ملفات صوتية هنا لاحقاً
    // const audio = new Audio(`/static/sounds/${type}.mp3`);
    // audio.play().catch(() => {}); // تجاهل الأخطاء إذا لم يُسمح بتشغيل الصوت
}

// تحميل قائمة المحادثات - مثل واتساب
async function loadChats() {
    console.log('🔄 Starting loadChats...');

    if (!authToken) {
        console.log('❌ No auth token available');
        showNotification('يرجى تسجيل الدخول أولاً', 'error');
        return;
    }

    // التأكد من وجود عنصر chatList
    if (!chatList) {
        chatList = document.getElementById('chatList');
        if (!chatList) {
            console.error('❌ chatList element not found');
            return;
        }
    }

    try {
        console.log('📡 Fetching conversations from API...');
        console.log('API URL:', `${API_BASE_URL}/messages/conversations`);

        const response = await fetch(`${API_BASE_URL}/messages/conversations`, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${authToken}`,
                'Content-Type': 'application/json'
            }
        });

        console.log('📡 Response status:', response.status);

        if (response.ok) {
            const conversations = await response.json();
            console.log('✅ API Response:', conversations);
            console.log('📊 Number of conversations:', conversations.length);

            // عرض المحادثات
            displayChatList(conversations);

            // إظهار رسالة نجاح
            if (conversations.length > 0) {
                console.log('✅ Conversations displayed successfully');
            } else {
                console.log('ℹ️ No conversations found');
            }

        } else {
            const errorText = await response.text();
            console.error('❌ API Error:', response.status, errorText);

            // إذا كان خطأ 401، فالمشكلة في المصادقة
            if (response.status === 401) {
                console.log('🔑 Authentication error, redirecting to login...');
                localStorage.removeItem('authToken');
                localStorage.removeItem('currentUser');
                authToken = null;
                currentUser = null;
                showLoginPage();
                showNotification('انتهت جلسة العمل، يرجى تسجيل الدخول مرة أخرى', 'warning');
                return;
            }

            // عرض رسالة خطأ مفصلة
            chatList.innerHTML = `
                <div class="error-message">
                    <i class="fas fa-exclamation-triangle"></i>
                    <h3>خطأ في تحميل المحادثات</h3>
                    <p>كود الخطأ: ${response.status}</p>
                    <p style="font-size: 12px; color: #999; margin-top: 8px;">${errorText}</p>
                    <button onclick="loadChats()" class="retry-btn">
                        <i class="fas fa-sync-alt"></i> إعادة المحاولة
                    </button>
                    <button onclick="location.reload()" class="retry-btn" style="margin-right: 8px; background: #dc3545;">
                        <i class="fas fa-refresh"></i> إعادة تحميل الصفحة
                    </button>
                </div>
            `;

            showNotification(`فشل في تحميل المحادثات (${response.status})`, 'error');
        }

    } catch (error) {
        console.error('❌ Network Error:', error);

        // عرض رسالة خطأ الشبكة
        chatList.innerHTML = `
            <div class="error-message">
                <i class="fas fa-wifi"></i>
                <h3>مشكلة في الاتصال</h3>
                <p>تحقق من اتصال الإنترنت</p>
                <button onclick="loadChats()" class="retry-btn">
                    <i class="fas fa-sync-alt"></i> إعادة المحاولة
                </button>
            </div>
        `;

        showNotification('مشكلة في الاتصال بالخادم', 'error');
    }
}

// عرض قائمة المحادثات - تصميم رائع وجميل
function displayChatList(conversations) {
    console.log('🎨 Displaying beautiful chat list...');

    // التأكد من وجود عنصر chatList
    if (!chatList) {
        chatList = document.getElementById('chatList');
        if (!chatList) {
            console.error('❌ chatList element not found!');
            return;
        }
    }

    // إضافة تأثير تحميل جميل
    chatList.innerHTML = '<div class="loading">🔄 جاري التحميل...</div>';

    setTimeout(() => {
        // مسح المحتوى السابق
        chatList.innerHTML = '';

        // التحقق من صحة البيانات
        if (!Array.isArray(conversations)) {
            console.error('❌ Conversations is not an array');
            chatList.innerHTML = `
                <div class="error-message">
                    <i class="fas fa-exclamation-triangle"></i>
                    <h3>خطأ في البيانات</h3>
                    <p>تنسيق البيانات غير صحيح</p>
                    <button onclick="loadChatsWithValidation()" class="retry-btn">
                        <i class="fas fa-sync-alt"></i> إعادة المحاولة
                    </button>
                </div>
            `;
            return;
        }

        // إذا لم تكن هناك محادثات
        if (conversations.length === 0) {
            chatList.innerHTML = `
                <div class="no-chats-message">
                    <i class="fas fa-comments"></i>
                    <h3>لا توجد محادثات بعد</h3>
                    <p>ابدأ محادثة جديدة بالضغط على زر +</p>
                </div>
            `;
            return;
        }

        // إنشاء عناصر المحادثات مع تأثيرات جميلة
        conversations.forEach((conversation, index) => {
            try {
                const chatItem = createChatItem(conversation);
                if (chatItem) {
                    // إضافة تأثير انزلاق جميل
                    chatItem.style.opacity = '0';
                    chatItem.style.transform = 'translateX(50px)';
                    chatList.appendChild(chatItem);

                    // تأثير ظهور متدرج
                    setTimeout(() => {
                        chatItem.style.transition = 'all 0.3s ease';
                        chatItem.style.opacity = '1';
                        chatItem.style.transform = 'translateX(0)';
                    }, index * 50); // تأخير متدرج لكل عنصر
                }
            } catch (error) {
                console.error(`❌ Error creating chat item ${index + 1}:`, error);
            }
        });

        console.log(`✨ Displayed ${conversations.length} conversations beautifully!`);

    }, 300); // تأخير قصير لإظهار تأثير التحميل
}

// تحديث قائمة المحادثات بعد رسالة جديدة - مثل واتساب
function updateChatListAfterMessage(messageData) {
    console.log('🔄 Updating chat list after new message...');

    // تحديث فوري للمحادثات
    setTimeout(() => {
        loadChats();
    }, 100);

    // إذا كانت المحادثة غير موجودة، أضفها
    const chatId = messageData.room_id ||
                   (messageData.sender_id === currentUser?.id ? messageData.recipient_id : messageData.sender_id);

    if (chatId && !document.querySelector(`[data-chat-id="${chatId}"]`)) {
        console.log('New conversation detected, reloading chat list...');
        setTimeout(() => {
            loadChats();
        }, 500);
    }
}

// دالة اختبار النظام
function testChatSystem() {
    console.log('🧪 Testing chat system...');
    console.log('Auth token:', authToken ? 'Present' : 'Missing');
    console.log('Current user:', currentUser);
    console.log('Chat list element:', document.getElementById('chatList'));
    console.log('API Base URL:', API_BASE_URL);

    // اختبار تحميل المحادثات
    loadChats();
}

// دالة للتحقق من صحة token
async function validateToken() {
    if (!authToken) {
        return false;
    }

    try {
        const response = await fetch(`${API_BASE_URL}/auth/me`, {
            headers: {
                'Authorization': `Bearer ${authToken}`
            }
        });

        if (response.ok) {
            const userData = await response.json();
            currentUser = userData;
            localStorage.setItem('currentUser', JSON.stringify(currentUser));
            return true;
        } else {
            console.log('Token validation failed:', response.status);
            return false;
        }
    } catch (error) {
        console.error('Error validating token:', error);
        return false;
    }
}

// دالة محسنة لتحميل المحادثات مع التحقق من token
async function loadChatsWithValidation() {
    console.log('🔐 Validating token before loading chats...');

    const isValidToken = await validateToken();
    if (!isValidToken) {
        console.log('❌ Invalid token, redirecting to login...');
        localStorage.removeItem('authToken');
        localStorage.removeItem('currentUser');
        authToken = null;
        currentUser = null;
        showLoginPage();
        showNotification('يرجى تسجيل الدخول مرة أخرى', 'warning');
        return;
    }

    console.log('✅ Token is valid, loading chats...');
    await loadChats();
}

// إضافة دالة للنافذة العامة للاختبار
window.testChatSystem = testChatSystem;
window.loadChats = loadChats;
window.loadChatsWithValidation = loadChatsWithValidation;
window.validateToken = validateToken;

// إنشاء عنصر محادثة - مثل واتساب
function createChatItem(conversation) {
    console.log('🔨 Creating chat item for:', conversation);

    // التحقق من صحة البيانات
    if (!conversation) {
        console.error('❌ Conversation is null or undefined');
        return null;
    }

    if (!conversation.id) {
        console.error('❌ Conversation missing ID:', conversation);
        return null;
    }

    if (!conversation.name) {
        console.error('❌ Conversation missing name:', conversation);
        return null;
    }

    try {
        const chatItem = document.createElement('div');
        chatItem.className = 'chat-item';
        chatItem.dataset.chatId = conversation.id;
        chatItem.dataset.chatType = conversation.is_group ? 'group' : 'direct';

        console.log('✅ Chat item element created with ID:', conversation.id);

        // معلومات الرسالة الأخيرة
        const lastMessage = conversation.last_message;
        const timeAgo = lastMessage && lastMessage.created_at ?
            formatTimeAgo(new Date(lastMessage.created_at)) : 'الآن';
        const preview = lastMessage && lastMessage.content ?
            (lastMessage.content.length > 30 ? lastMessage.content.substring(0, 30) + '...' : lastMessage.content) :
            'ابدأ المحادثة...';

        // الصورة الرمزية
        const avatarContent = conversation.avatar_url ?
            `<img src="${conversation.avatar_url}" alt="${conversation.name}">` :
            `<div class="avatar-text">${conversation.name.charAt(0).toUpperCase()}</div>`;

        // تصميم رائع وجميل
        chatItem.innerHTML = `
            <div class="chat-avatar">
                ${avatarContent}
                ${conversation.is_group ? '<i class="fas fa-users group-badge"></i>' : ''}
                ${conversation.is_online ? '<div class="online-indicator"></div>' : ''}
            </div>
            <div class="chat-content">
                <div class="chat-header">
                    <h4 class="chat-name">${conversation.name}</h4>
                    <div class="chat-header-actions">
                        <span class="chat-time">${timeAgo}</span>
                        <div class="chat-options">
                            <button class="chat-options-btn" onclick="toggleChatOptions(event, ${conversation.id})">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                            <div class="chat-options-menu" id="chatOptions${conversation.id}">
                                <button onclick="deleteChatConversation(${conversation.id}, '${conversation.name}')">
                                    <i class="fas fa-trash"></i> حذف المحادثة
                                </button>
                                <button onclick="archiveConversation(${conversation.id})">
                                    <i class="fas fa-archive"></i> أرشفة
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="chat-preview">
                    <span class="preview-text">${preview}</span>
                    ${conversation.unread_count > 0 ? `<span class="unread-badge pulse">${conversation.unread_count}</span>` : ''}
                </div>
            </div>
        `;

        // إضافة حدث النقر
        chatItem.addEventListener('click', () => {
            console.log('🖱️ Chat item clicked:', conversation);
            openChat(conversation);
        });

        console.log('✅ Chat item created successfully');
        return chatItem;

    } catch (error) {
        console.error('❌ Error in createChatItem:', error);
        return null;
    }
}

// فتح محادثة
async function openChat(conversation) {
    currentChatId = conversation.id;
    currentChatType = conversation.is_group ? 'group' : 'direct';

    // إعادة تعيين كل شيء للمحادثة الجديدة
    console.log('🔄 Opening conversation:', conversation.id);
    resetPaginationState();
    cleanupIndicators();
    clearTypingStatus();

    // تحديث واجهة المستخدم
    document.querySelectorAll('.chat-item').forEach(item => {
        item.classList.remove('active');
    });

    const chatElement = document.querySelector(`[data-chat-id="${conversation.id}"]`);
    if (chatElement) {
        chatElement.classList.add('active');
    }

    // إخفاء شاشة الترحيب وإظهار نافذة الدردشة
    const welcomeScreen = document.getElementById('welcomeScreen');
    const chatWindow = document.getElementById('chatWindow');

    if (welcomeScreen) welcomeScreen.style.display = 'none';
    if (chatWindow) chatWindow.style.display = 'flex';

    // تحديث معلومات جهة الاتصال
    const chatContactName = document.getElementById('chatContactName');
    const chatContactStatus = document.getElementById('chatContactStatus');

    if (chatContactName) {
        chatContactName.textContent = conversation.name;
    }

    if (chatContactStatus) {
        if (conversation.is_group) {
            chatContactStatus.textContent = 'مجموعة';
            chatContactStatus.className = 'chat-contact-status group';
        } else {
            chatContactStatus.textContent = conversation.is_online ? 'متصل' : 'غير متصل';
            chatContactStatus.className = `chat-contact-status ${conversation.is_online ? 'online' : 'offline'}`;
        }
    }

    // تحميل الرسائل
    await loadMessages(conversation.id);

    // إضافة مستمع التمرير للـ infinite scroll
    setupInfiniteScroll();

    // تشخيص التمرير
    setTimeout(() => {
        diagnoseScrolling();
    }, 1000);
}

// تحميل الرسائل - نسخة مبسطة
async function loadMessages(conversationId, page = 1, append = false) {
    // منع التحميل المتعدد
    if (isLoadingMessages) {
        console.log('⏳ Already loading messages, skipping...');
        return;
    }

    // منع إعادة التحميل للصفحة الأولى
    if (page === 1 && currentLoadedConversation === conversationId && messagesContainer.children.length > 0) {
        console.log('✅ Messages already loaded for this conversation');
        return;
    }

    isLoadingMessages = true;
    console.log(`🔄 Loading messages - Page: ${page}, Append: ${append}`);

    try {
        const endpoint = `${API_BASE_URL}/messages/conversation/${conversationId}?page=${page}&size=20`;
        const response = await fetch(endpoint, {
            headers: { 'Authorization': `Bearer ${authToken}` }
        });

        if (response.ok) {
            const data = await response.json();
            const messages = data.messages || data; // دعم كلا التنسيقين

            console.log(`📨 Received ${messages.length} messages`);

            if (append) {
                // تحميل رسائل قديمة
                if (messages.length === 0) {
                    console.log('🛑 No more messages - reached the beginning');
                    hasMoreMessages = false;
                    showEndOfMessagesIndicator();
                } else {
                    prependMessages(messages);
                    hasMoreMessages = messages.length === 20;
                    console.log(`✅ Added ${messages.length} older messages. Has more: ${hasMoreMessages}`);
                }
            } else {
                // تحميل أول مرة
                displayMessages(messages);
                currentLoadedConversation = conversationId;
                currentPage = 1;
                hasMoreMessages = messages.length === 20;
                console.log(`📱 Initial load: ${messages.length} messages. Has more: ${hasMoreMessages}`);
            }
        } else {
            console.error('❌ Failed to load messages:', response.status);
            if (!append) displayMessages([]);
        }
    } catch (error) {
        console.error('❌ Error loading messages:', error);
        if (!append) displayMessages([]);
    } finally {
        isLoadingMessages = false;
        console.log('✅ Loading complete');
    }
}

// عرض الرسائل
function displayMessages(messages) {
    messagesContainer.innerHTML = '';

    // عكس ترتيب الرسائل للصفحة الأولى (API يرجع الأحدث أولاً)
    const sortedMessages = [...messages].reverse();

    sortedMessages.forEach(message => {
        const messageElement = createMessageElement(message);
        messagesContainer.appendChild(messageElement);
    });

    // التمرير إلى أسفل (إجباري عند تحميل المحادثة)
    scrollToBottom(true);
}

// إضافة رسائل قديمة في الأعلى
function prependMessages(messages) {
    // إخفاء مؤشر التحميل
    hideLoadingIndicator();

    if (messages.length === 0) {
        console.log('📭 No messages to prepend');
        return;
    }

    console.log(`📝 Prepending ${messages.length} messages`);

    // حفظ موقع التمرير الحالي
    const scrollHeight = messagesContainer.scrollHeight;
    const scrollTop = messagesContainer.scrollTop;

    // التحقق من عدم وجود رسائل مكررة
    const existingMessageIds = new Set();
    Array.from(messagesContainer.children).forEach(child => {
        const messageId = child.dataset.messageId;
        if (messageId) existingMessageIds.add(messageId);
    });

    // إضافة الرسائل في الأعلى (فقط الرسائل غير المكررة)
    let addedCount = 0;
    messages.forEach(message => {
        if (!existingMessageIds.has(message.id.toString())) {
            const messageElement = createMessageElement(message);
            messagesContainer.insertBefore(messageElement, messagesContainer.firstChild);
            addedCount++;
        } else {
            console.log('⚠️ Skipping duplicate message:', message.id);
        }
    });

    console.log(`✅ Added ${addedCount} new messages (skipped ${messages.length - addedCount} duplicates)`);

    // الحفاظ على موقع التمرير
    const newScrollHeight = messagesContainer.scrollHeight;
    messagesContainer.scrollTop = scrollTop + (newScrollHeight - scrollHeight);
}

// إعداد infinite scroll للرسائل القديمة
function setupInfiniteScroll() {
    if (!messagesContainer) return;

    // إزالة المستمع السابق إن وجد
    messagesContainer.removeEventListener('scroll', handleInfiniteScroll);
    messagesContainer.removeEventListener('scroll', handleScrollEffects);

    // إضافة المستمعين الجدد
    messagesContainer.addEventListener('scroll', handleInfiniteScroll);
    messagesContainer.addEventListener('scroll', handleScrollEffects);

    // تأكيد أن التمرير يعمل
    console.log('✅ Infinite scroll setup complete');
    console.log('Messages container height:', messagesContainer.offsetHeight);
    console.log('Messages container scroll height:', messagesContainer.scrollHeight);
}

// معالج infinite scroll - مبسط
function handleInfiniteScroll() {
    const scrollTop = messagesContainer.scrollTop;

    // شروط بسيطة وواضحة
    const nearTop = scrollTop <= 50;
    const canLoadMore = hasMoreMessages && !isLoadingMessages && currentChatId;

    if (nearTop && canLoadMore) {
        console.log('🔄 Triggering infinite scroll - Loading page ' + (currentPage + 1));
        showLoadingIndicator();
        currentPage++;
        loadMessages(currentChatId, currentPage, true);
    } else if (nearTop && !hasMoreMessages) {
        console.log('🛑 At top but no more messages available');
    }
}

// إظهار مؤشر التحميل
function showLoadingIndicator() {
    // التحقق من وجود المؤشر مسبقاً
    if (document.getElementById('loadingIndicator')) return;

    const loadingDiv = document.createElement('div');
    loadingDiv.id = 'loadingIndicator';
    loadingDiv.className = 'loading-indicator';
    loadingDiv.innerHTML = `
        <div class="loading-spinner"></div>
        <span>جاري تحميل الرسائل القديمة...</span>
    `;

    messagesContainer.insertBefore(loadingDiv, messagesContainer.firstChild);
}

// إخفاء مؤشر التحميل
function hideLoadingIndicator() {
    const loadingDiv = document.getElementById('loadingIndicator');
    if (loadingDiv) {
        loadingDiv.remove();
    }
}

// إظهار مؤشر نهاية المحادثة
function showEndOfMessagesIndicator() {
    // التحقق من وجود المؤشر مسبقاً
    if (document.getElementById('endOfMessagesIndicator')) return;

    const endDiv = document.createElement('div');
    endDiv.id = 'endOfMessagesIndicator';
    endDiv.className = 'end-of-messages-indicator';
    endDiv.innerHTML = `
        <div class="end-icon">
            <i class="fas fa-flag-checkered"></i>
        </div>
        <span>🎉 وصلت لبداية المحادثة</span>
    `;

    messagesContainer.insertBefore(endDiv, messagesContainer.firstChild);

    // إزالة المؤشر بعد 3 ثوان
    setTimeout(() => {
        if (endDiv.parentElement) {
            endDiv.style.transition = 'all 0.5s ease';
            endDiv.style.opacity = '0';
            endDiv.style.transform = 'translateY(-20px)';
            setTimeout(() => {
                if (endDiv.parentElement) {
                    endDiv.remove();
                }
            }, 500);
        }
    }, 3000);
}

// معالج تأثيرات التمرير
let scrollTimeout;
function handleScrollEffects() {
    if (!messagesContainer) return;

    // إضافة فئة التمرير
    messagesContainer.classList.add('scrolling');

    // إزالة فئة التمرير بعد توقف التمرير
    clearTimeout(scrollTimeout);
    scrollTimeout = setTimeout(() => {
        messagesContainer.classList.remove('scrolling');
    }, 150);

    // تسجيل معلومات التمرير للتشخيص
    console.log('Scroll position:', messagesContainer.scrollTop);
    console.log('Scroll height:', messagesContainer.scrollHeight);
    console.log('Client height:', messagesContainer.clientHeight);
}

// تشخيص التمرير
function diagnoseScrolling() {
    if (!messagesContainer) {
        console.error('❌ Messages container not found!');
        return;
    }

    console.log('🔍 Scroll Diagnosis:');
    console.log('- Container exists:', !!messagesContainer);
    console.log('- Container height:', messagesContainer.offsetHeight + 'px');
    console.log('- Container scroll height:', messagesContainer.scrollHeight + 'px');
    console.log('- Container client height:', messagesContainer.clientHeight + 'px');
    console.log('- Overflow Y:', getComputedStyle(messagesContainer).overflowY);
    console.log('- Messages count:', messagesContainer.children.length);
    console.log('- Is scrollable:', messagesContainer.scrollHeight > messagesContainer.clientHeight);

    // اختبار التمرير
    if (messagesContainer.scrollHeight > messagesContainer.clientHeight) {
        console.log('✅ Container is scrollable!');
    } else {
        console.log('⚠️ Container is not scrollable - not enough content');
    }
}

// إعادة تعيين حالة pagination
function resetPaginationState() {
    currentLoadedConversation = null;
    currentPage = 1;
    hasMoreMessages = true;
    isLoadingMessages = false;
    console.log('🔄 Pagination state reset');
}

// تنظيف المؤشرات
function cleanupIndicators() {
    hideLoadingIndicator();
    const endIndicator = document.getElementById('endOfMessagesIndicator');
    if (endIndicator) {
        endIndicator.remove();
        console.log('🧹 Cleaned up indicators');
    }
}

// إنشاء عنصر رسالة
function createMessageElement(message) {
    const messageDiv = document.createElement('div');
    const isSent = message.sender_id === currentUser.id;

    messageDiv.className = `message ${isSent ? 'sent' : 'received'}`;
    messageDiv.setAttribute('data-message-id', message.id);

    const time = formatTime(new Date(message.created_at));
    const senderName = isSent ? 'أنت' : message.sender_name || 'مستخدم';

    // إنشاء محتوى الرسالة حسب النوع
    let messageContent = '';
    const contentType = message.content_type || 'text';

    switch (contentType) {
        case 'image':
            messageContent = createImageMessage(message);
            break;
        case 'file':
        case 'document':
            messageContent = createFileMessage(message);
            break;
        case 'audio':
        case 'voice':
            messageContent = createAudioMessage(message);
            break;
        case 'video':
            messageContent = createVideoMessage(message);
            break;
        default:
            messageContent = `<div class="message-text">${message.content}</div>`;
    }

    messageDiv.innerHTML = `
        <div class="message-avatar">
            ${senderName.charAt(0).toUpperCase()}
        </div>
        <div class="message-content">
            <div class="message-bubble ${contentType}-message">
                ${messageContent}
                ${message.reply_to ? createReplyPreview(message.reply_to) : ''}
                ${isSent ? `
                    <div class="message-actions">
                        <button class="message-action-btn edit-btn" onclick="editMessage(${message.id})" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="message-action-btn delete-btn" onclick="deleteMessage(${message.id})" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                ` : ''}
            </div>
            <div class="message-time">${time} ${message.is_edited ? '(معدلة)' : ''}</div>
            ${isSent ? createMessageStatus(message.status) : ''}
        </div>
    `;

    // إضافة أنيميشن للرسائل الجديدة
    if (!message.temp) {
        messageDiv.style.opacity = '0';
        messageDiv.style.transform = 'translateY(20px)';
        setTimeout(() => {
            messageDiv.style.transition = 'all 0.3s ease';
            messageDiv.style.opacity = '1';
            messageDiv.style.transform = 'translateY(0)';
        }, 50);
    }

    return messageDiv;
}

// إنشاء رسالة صورة
function createImageMessage(message) {
    return `
        <div class="image-message">
            <img src="${message.file_url}" alt="${message.file_name}"
                 onclick="openImageViewer('${message.file_url}', '${message.file_name}')"
                 loading="lazy">
            ${message.content && message.content !== message.file_name ?
                `<div class="image-caption">${message.content}</div>` : ''}
        </div>
    `;
}

// إنشاء رسالة ملف
function createFileMessage(message) {
    const fileSize = message.file_size ? formatFileSize(message.file_size) : '';
    const fileIcon = getFileIcon(message.file_type);

    return `
        <div class="file-message">
            <div class="file-icon">
                <i class="fas ${fileIcon}"></i>
            </div>
            <div class="file-details">
                <div class="file-name">${message.file_name || message.content}</div>
                <div class="file-info">${fileSize}</div>
            </div>
            <a href="${message.file_url}" download="${message.file_name}" class="file-download">
                <i class="fas fa-download"></i>
            </a>
        </div>
    `;
}

// إنشاء رسالة صوتية
function createAudioMessage(message) {
    const duration = message.metadata?.duration || 0;

    return `
        <div class="audio-message">
            <button class="audio-play-btn" onclick="toggleAudio(this, '${message.file_url}')">
                <i class="fas fa-play"></i>
            </button>
            <div class="audio-waveform">
                <div class="audio-progress"></div>
            </div>
            <div class="audio-duration">${formatDuration(duration)}</div>
        </div>
    `;
}

// إنشاء رسالة فيديو
function createVideoMessage(message) {
    return `
        <div class="video-message">
            <video controls preload="metadata">
                <source src="${message.file_url}" type="${message.file_type}">
                متصفحك لا يدعم تشغيل الفيديو
            </video>
            ${message.content && message.content !== message.file_name ?
                `<div class="video-caption">${message.content}</div>` : ''}
        </div>
    `;
}

// إنشاء معاينة الرد
function createReplyPreview(replyMessage) {
    return `
        <div class="reply-preview">
            <div class="reply-line"></div>
            <div class="reply-content">
                <div class="reply-sender">${replyMessage.sender_name || 'مستخدم'}</div>
                <div class="reply-text">${replyMessage.content}</div>
            </div>
        </div>
    `;
}

// إنشاء حالة الرسالة
function createMessageStatus(status) {
    const statusIcons = {
        sent: 'fa-check',
        delivered: 'fa-check-double',
        read: 'fa-check-double'
    };

    const statusClasses = {
        sent: 'sent',
        delivered: 'delivered',
        read: 'read'
    };

    return `
        <div class="message-status ${statusClasses[status] || 'sent'}">
            <i class="fas ${statusIcons[status] || 'fa-check'}"></i>
        </div>
    `;
}

// الحصول على أيقونة الملف
function getFileIcon(fileType) {
    if (!fileType) return 'fa-file';

    if (fileType.includes('pdf')) return 'fa-file-pdf';
    if (fileType.includes('word') || fileType.includes('document')) return 'fa-file-word';
    if (fileType.includes('excel') || fileType.includes('sheet')) return 'fa-file-excel';
    if (fileType.includes('powerpoint') || fileType.includes('presentation')) return 'fa-file-powerpoint';
    if (fileType.includes('text')) return 'fa-file-alt';
    if (fileType.includes('zip') || fileType.includes('rar')) return 'fa-file-archive';

    return 'fa-file';
}

// تنسيق حجم الملف
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// تنسيق مدة الصوت
function formatDuration(seconds) {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
}

// دوال المجموعات
let selectedMembers = new Set();

// إظهار نافذة إنشاء مجموعة جديدة
function showNewGroupModal() {
    document.getElementById('newGroupModal').style.display = 'flex';
    selectedMembers.clear();
    updateSelectedMembersDisplay();
    loadMembersForGroup();
}

// إخفاء نافذة إنشاء مجموعة
function hideNewGroupModal() {
    document.getElementById('newGroupModal').style.display = 'none';
    document.getElementById('createGroupForm').reset();
    selectedMembers.clear();
    updateSelectedMembersDisplay();
}

// تحميل المستخدمين لإضافتهم للمجموعة
async function loadMembersForGroup() {
    try {
        const response = await fetch(`${API_BASE_URL}/users/?size=100`, {
            headers: {
                'Authorization': `Bearer ${authToken}`
            }
        });

        if (response.ok) {
            const data = await response.json();
            // التحقق من نوع البيانات المُعادة
            let users = [];
            if (Array.isArray(data)) {
                users = data;
            } else if (data.items && Array.isArray(data.items)) {
                users = data.items;
            } else {
                console.error('Unexpected members data format:', data);
                showNotification('خطأ في تحميل قائمة المستخدمين', 'error');
                return;
            }

            displayMembersList(users.filter(user => user.id !== currentUser?.id));
        } else {
            console.error('Failed to load members:', response.status);
            showNotification('فشل في تحميل قائمة المستخدمين', 'error');
        }
    } catch (error) {
        console.error('Error loading members:', error);
        showNotification('خطأ في الاتصال بالخادم', 'error');
    }
}

// عرض قائمة الأعضاء
function displayMembersList(members) {
    const membersList = document.getElementById('membersList');

    if (members.length === 0) {
        membersList.innerHTML = '<div class="no-members">لا توجد مستخدمين متاحين</div>';
        return;
    }

    membersList.innerHTML = members.map(member => `
        <div class="member-item" onclick="toggleMemberSelection(${member.id}, '${member.username}')">
            <div class="member-avatar">
                ${member.username.charAt(0).toUpperCase()}
            </div>
            <div class="member-info">
                <div class="member-name">${member.username}</div>
                <div class="member-status">${member.is_online ? 'متصل' : 'غير متصل'}</div>
            </div>
            <div class="member-select-indicator">
                <i class="fas fa-check" style="display: none;"></i>
            </div>
        </div>
    `).join('');
}

// تبديل اختيار عضو
function toggleMemberSelection(memberId, memberName) {
    const memberItem = document.querySelector(`[onclick="toggleMemberSelection(${memberId}, '${memberName}')"]`);
    const indicator = memberItem.querySelector('.member-select-indicator i');

    if (selectedMembers.has(memberId)) {
        selectedMembers.delete(memberId);
        memberItem.classList.remove('selected');
        indicator.style.display = 'none';
    } else {
        selectedMembers.add(memberId);
        memberItem.classList.add('selected');
        indicator.style.display = 'block';
    }

    updateSelectedMembersDisplay();
}

// تحديث عرض الأعضاء المختارين
function updateSelectedMembersDisplay() {
    const selectedMembersContainer = document.getElementById('selectedMembers');

    if (selectedMembers.size === 0) {
        selectedMembersContainer.innerHTML = '<div class="no-selection">لم يتم اختيار أعضاء بعد</div>';
        return;
    }

    const memberElements = Array.from(selectedMembers).map(memberId => {
        const memberItem = document.querySelector(`[onclick*="${memberId}"]`);
        const memberName = memberItem ? memberItem.querySelector('.member-name').textContent : `User ${memberId}`;

        return `
            <div class="selected-member">
                <span>${memberName}</span>
                <button class="remove-member" onclick="removeMember(${memberId})">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
    }).join('');

    selectedMembersContainer.innerHTML = memberElements;
}

// إزالة عضو من الاختيار
function removeMember(memberId) {
    selectedMembers.delete(memberId);
    const memberItem = document.querySelector(`[onclick*="${memberId}"]`);
    if (memberItem) {
        memberItem.classList.remove('selected');
        memberItem.querySelector('.member-select-indicator i').style.display = 'none';
    }
    updateSelectedMembersDisplay();
}

// البحث في الأعضاء
function searchMembers(event) {
    const searchTerm = event.target.value.toLowerCase();
    const memberItems = document.querySelectorAll('.member-item');

    memberItems.forEach(item => {
        const memberName = item.querySelector('.member-name').textContent.toLowerCase();
        if (memberName.includes(searchTerm)) {
            item.style.display = 'flex';
        } else {
            item.style.display = 'none';
        }
    });
}

// إنشاء مجموعة جديدة
async function createGroup(event) {
    event.preventDefault();

    const formData = new FormData(event.target);
    const groupData = {
        name: formData.get('groupName'),
        description: formData.get('groupDescription'),
        room_type: formData.get('groupType'),
        member_ids: Array.from(selectedMembers)
    };

    if (!groupData.name.trim()) {
        showNotification('يرجى إدخال اسم المجموعة', 'error');
        return;
    }

    if (selectedMembers.size === 0) {
        showNotification('يرجى اختيار عضو واحد على الأقل', 'error');
        return;
    }

    try {
        const response = await fetch(`${API_BASE_URL}/rooms/`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${authToken}`
            },
            body: JSON.stringify(groupData)
        });

        if (response.ok) {
            const newGroup = await response.json();
            showNotification(`تم إنشاء المجموعة "${groupData.name}" بنجاح`, 'success');
            hideNewGroupModal();
            loadChats(); // إعادة تحميل قائمة المحادثات
        } else {
            const errorData = await response.json();
            console.error('Group creation failed:', errorData);
            showNotification(errorData.detail || 'فشل في إنشاء المجموعة', 'error');
        }
    } catch (error) {
        console.error('Error creating group:', error);
        showNotification('خطأ في الاتصال بالخادم', 'error');
    }
}

// دوال البحث والأرشفة
let searchResults = [];
let currentSearchTerm = '';

// إظهار/إخفاء شريط البحث
function toggleSearchBar() {
    const searchBar = document.getElementById('searchBar');
    const isVisible = searchBar.style.display !== 'none';

    if (isVisible) {
        hideSearchBar();
    } else {
        showSearchBar();
    }
}

function showSearchBar() {
    const searchBar = document.getElementById('searchBar');
    const searchInput = document.getElementById('messageSearchInput');

    searchBar.style.display = 'block';
    searchInput.focus();
}

function hideSearchBar() {
    const searchBar = document.getElementById('searchBar');
    const searchInput = document.getElementById('messageSearchInput');
    const searchResults = document.getElementById('searchResults');

    searchBar.style.display = 'none';
    searchInput.value = '';
    searchResults.innerHTML = '';
    currentSearchTerm = '';

    // إزالة التمييز من الرسائل
    clearSearchHighlights();
}

// البحث في الرسائل
async function searchMessages(event) {
    const searchTerm = event.target.value.trim();
    currentSearchTerm = searchTerm;

    if (searchTerm.length < 2) {
        document.getElementById('searchResults').innerHTML = '';
        clearSearchHighlights();
        return;
    }

    if (!currentChatId) {
        document.getElementById('searchResults').innerHTML =
            '<div class="no-search-results"><i class="fas fa-comments"></i><p>اختر محادثة للبحث فيها</p></div>';
        return;
    }

    try {
        const response = await fetch(`/api/v1/messages/search?q=${encodeURIComponent(searchTerm)}&chat_id=${currentChatId}`, {
            headers: {
                'Authorization': `Bearer ${authToken}`
            }
        });

        if (response.ok) {
            const results = await response.json();
            displaySearchResults(results, searchTerm);
            highlightMessagesInChat(results, searchTerm);
        } else {
            showNotification('فشل في البحث', 'error');
        }
    } catch (error) {
        console.error('Search error:', error);
        showNotification('خطأ في البحث', 'error');
    }
}

// عرض نتائج البحث
function displaySearchResults(results, searchTerm) {
    const searchResultsContainer = document.getElementById('searchResults');

    if (results.length === 0) {
        searchResultsContainer.innerHTML = `
            <div class="no-search-results">
                <i class="fas fa-search"></i>
                <p>لا توجد نتائج للبحث عن "${searchTerm}"</p>
            </div>
        `;
        return;
    }

    const resultsHTML = results.map(message => {
        const highlightedContent = highlightSearchTerm(message.content, searchTerm);
        const messageTime = formatTime(new Date(message.created_at));

        return `
            <div class="search-result-item" onclick="scrollToMessage(${message.id})">
                <div class="search-result-content">${highlightedContent}</div>
                <div class="search-result-meta">
                    <span>${message.sender_name || 'مستخدم'}</span>
                    <span>${messageTime}</span>
                </div>
            </div>
        `;
    }).join('');

    searchResultsContainer.innerHTML = resultsHTML;
}

// تمييز النص المطابق
function highlightSearchTerm(text, searchTerm) {
    if (!searchTerm) return text;

    const regex = new RegExp(`(${escapeRegExp(searchTerm)})`, 'gi');
    return text.replace(regex, '<span class="search-highlight">$1</span>');
}

// تنظيف الرموز الخاصة للتعبير النمطي
function escapeRegExp(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

// تمييز الرسائل في المحادثة
function highlightMessagesInChat(results, searchTerm) {
    clearSearchHighlights();

    results.forEach(message => {
        const messageElement = document.querySelector(`[data-message-id="${message.id}"]`);
        if (messageElement) {
            const contentElement = messageElement.querySelector('.message-text');
            if (contentElement) {
                contentElement.innerHTML = highlightSearchTerm(message.content, searchTerm);
                messageElement.classList.add('search-highlighted');
            }
        }
    });
}

// إزالة التمييز من الرسائل
function clearSearchHighlights() {
    const highlightedMessages = document.querySelectorAll('.search-highlighted');
    highlightedMessages.forEach(message => {
        message.classList.remove('search-highlighted');
        const contentElement = message.querySelector('.message-text');
        if (contentElement) {
            // استعادة النص الأصلي
            const originalText = contentElement.textContent;
            contentElement.innerHTML = originalText;
        }
    });
}

// التمرير إلى رسالة معينة
function scrollToMessage(messageId) {
    const messageElement = document.querySelector(`[data-message-id="${messageId}"]`);
    if (messageElement) {
        messageElement.scrollIntoView({ behavior: 'smooth', block: 'center' });

        // إضافة تأثير بصري مؤقت
        messageElement.style.backgroundColor = '#fff3cd';
        setTimeout(() => {
            messageElement.style.backgroundColor = '';
        }, 2000);
    }
}

// إظهار خيارات إضافية
function showMoreOptions() {
    // سيتم تطوير هذه الوظيفة لاحقاً
    showNotification('خيارات إضافية قيد التطوير', 'info');
}

// إضافة رسالة للمفضلة
function toggleFavoriteMessage(messageId) {
    // سيتم تطوير هذه الوظيفة لاحقاً
    showNotification('المفضلة قيد التطوير', 'info');
}

// أرشفة رسالة
function archiveMessage(messageId) {
    // سيتم تطوير هذه الوظيفة لاحقاً
    showNotification('الأرشفة قيد التطوير', 'info');
}

// دوال الملف الشخصي والإعدادات
let userSettings = {
    darkMode: false,
    fontSize: 'medium',
    enterToSend: true,
    typingIndicator: true,
    desktopNotifications: true,
    soundNotifications: true,
    notificationSound: 'default'
};

// إظهار نافذة الملف الشخصي
function showProfileModal() {
    document.getElementById('profileModal').style.display = 'flex';
    loadUserProfile();
    loadUserSettings();
}

// إخفاء نافذة الملف الشخصي
function hideProfileModal() {
    document.getElementById('profileModal').style.display = 'none';
}

// تحميل بيانات المستخدم
function loadUserProfile() {
    if (currentUser) {
        document.getElementById('profileUsername').value = currentUser.username || '';
        document.getElementById('profileEmail').value = currentUser.email || '';
        document.getElementById('profileDisplayName').value = currentUser.display_name || '';
        document.getElementById('profileBio').value = currentUser.bio || '';
        document.getElementById('profileStatus').value = currentUser.status || 'online';

        // تحديث الصورة الرمزية
        const profileInitial = document.getElementById('profileInitial');
        profileInitial.textContent = currentUser.username ? currentUser.username.charAt(0).toUpperCase() : 'U';

        if (currentUser.avatar_url) {
            const profileAvatar = document.getElementById('profileAvatar');
            profileAvatar.innerHTML = `<img src="${currentUser.avatar_url}" alt="صورة المستخدم">`;
        }
    }
}

// تحميل إعدادات المستخدم
function loadUserSettings() {
    // تحميل الإعدادات من localStorage
    const savedSettings = localStorage.getItem('userSettings');
    if (savedSettings) {
        userSettings = { ...userSettings, ...JSON.parse(savedSettings) };
    }

    // تطبيق الإعدادات على الواجهة
    document.getElementById('darkModeToggle').checked = userSettings.darkMode;
    document.getElementById('fontSizeSelect').value = userSettings.fontSize;
    document.getElementById('enterToSendToggle').checked = userSettings.enterToSend;
    document.getElementById('typingIndicatorToggle').checked = userSettings.typingIndicator;
    document.getElementById('desktopNotificationsToggle').checked = userSettings.desktopNotifications;
    document.getElementById('soundNotificationsToggle').checked = userSettings.soundNotifications;
    document.getElementById('notificationSoundSelect').value = userSettings.notificationSound;

    // تحديث إعدادات الإشعارات المتقدمة
    updateNotificationSettings();

    // تطبيق الإعدادات
    applySettings();
}

// حفظ الإعدادات
function saveUserSettings() {
    localStorage.setItem('userSettings', JSON.stringify(userSettings));
}

// تطبيق الإعدادات
function applySettings() {
    // الوضع المظلم
    if (userSettings.darkMode) {
        document.body.classList.add('dark-mode');
    } else {
        document.body.classList.remove('dark-mode');
    }

    // حجم الخط
    document.body.classList.remove('font-small', 'font-medium', 'font-large');
    document.body.classList.add(`font-${userSettings.fontSize}`);
}

// تحديث الملف الشخصي
async function updateProfile(event) {
    event.preventDefault();

    const formData = new FormData(event.target);
    const profileData = {
        email: formData.get('email'),
        display_name: formData.get('displayName'),
        bio: formData.get('bio'),
        status: formData.get('status')
    };

    try {
        const response = await fetch('/api/v1/users/profile', {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${authToken}`
            },
            body: JSON.stringify(profileData)
        });

        if (response.ok) {
            const updatedUser = await response.json();
            currentUser = { ...currentUser, ...updatedUser };
            showNotification('تم تحديث الملف الشخصي بنجاح', 'success');
            hideProfileModal();
        } else {
            const errorData = await response.json();
            showNotification(errorData.detail || 'فشل في تحديث الملف الشخصي', 'error');
        }
    } catch (error) {
        console.error('Profile update error:', error);
        showNotification('خطأ في الاتصال بالخادم', 'error');
    }
}

// تغيير الصورة الرمزية
function handleAvatarChange(event) {
    const file = event.target.files[0];
    if (file) {
        if (file.size > 5 * 1024 * 1024) { // 5MB
            showNotification('حجم الصورة كبير جداً. الحد الأقصى 5MB', 'error');
            return;
        }

        if (!file.type.startsWith('image/')) {
            showNotification('يرجى اختيار ملف صورة صالح', 'error');
            return;
        }

        uploadAvatar(file);
    }
}

// رفع الصورة الرمزية
async function uploadAvatar(file) {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('type', 'image');

    try {
        const response = await fetch('/api/v1/files/upload', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${authToken}`
            },
            body: formData
        });

        if (response.ok) {
            const result = await response.json();

            // تحديث الصورة الرمزية في الملف الشخصي
            const profileAvatar = document.getElementById('profileAvatar');
            profileAvatar.innerHTML = `<img src="${result.file_url}" alt="صورة المستخدم">`;

            // تحديث الصورة في قاعدة البيانات
            await updateUserAvatar(result.file_url);

            showNotification('تم تحديث الصورة الرمزية بنجاح', 'success');
        } else {
            showNotification('فشل في رفع الصورة', 'error');
        }
    } catch (error) {
        console.error('Avatar upload error:', error);
        showNotification('خطأ في رفع الصورة', 'error');
    }
}

// تحديث رابط الصورة الرمزية
async function updateUserAvatar(avatarUrl) {
    try {
        const response = await fetch('/api/v1/users/avatar', {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${authToken}`
            },
            body: JSON.stringify({ avatar_url: avatarUrl })
        });

        if (response.ok) {
            currentUser.avatar_url = avatarUrl;
        }
    } catch (error) {
        console.error('Avatar update error:', error);
    }
}

// تبديل التبويبات
function switchTab(tabName) {
    // إزالة الفئة النشطة من جميع الأزرار والألواح
    document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
    document.querySelectorAll('.tab-panel').forEach(panel => panel.classList.remove('active'));

    // إضافة الفئة النشطة للتبويب المحدد
    document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');
    document.getElementById(`${tabName}-tab`).classList.add('active');
}

// تبديل الوضع المظلم
function toggleDarkMode(event) {
    userSettings.darkMode = event.target.checked;
    applySettings();
    saveUserSettings();
    showNotification(userSettings.darkMode ? 'تم تفعيل الوضع المظلم' : 'تم إلغاء الوضع المظلم', 'info');
}

// تغيير حجم الخط
function changeFontSize(event) {
    userSettings.fontSize = event.target.value;
    applySettings();
    saveUserSettings();
    showNotification(`تم تغيير حجم الخط إلى ${event.target.options[event.target.selectedIndex].text}`, 'info');
}

// تبديل إرسال بـ Enter
function toggleEnterToSend(event) {
    userSettings.enterToSend = event.target.checked;
    saveUserSettings();
    showNotification(userSettings.enterToSend ? 'تم تفعيل الإرسال بـ Enter' : 'تم إلغاء الإرسال بـ Enter', 'info');
}

// تبديل إشعارات سطح المكتب
function toggleDesktopNotifications(event) {
    userSettings.desktopNotifications = event.target.checked;

    if (userSettings.desktopNotifications) {
        // طلب إذن الإشعارات
        if ('Notification' in window) {
            Notification.requestPermission().then(permission => {
                if (permission === 'granted') {
                    showNotification('تم تفعيل إشعارات سطح المكتب', 'success');
                } else {
                    userSettings.desktopNotifications = false;
                    event.target.checked = false;
                    showNotification('تم رفض إذن الإشعارات', 'error');
                }
            });
        } else {
            userSettings.desktopNotifications = false;
            event.target.checked = false;
            showNotification('المتصفح لا يدعم إشعارات سطح المكتب', 'error');
        }
    } else {
        showNotification('تم إلغاء إشعارات سطح المكتب', 'info');
    }

    saveUserSettings();
}

// إظهار إشعار سطح المكتب
function showDesktopNotification(title, body, icon) {
    if (userSettings.desktopNotifications && 'Notification' in window && Notification.permission === 'granted') {
        new Notification(title, {
            body: body,
            icon: icon || '/static/images/logo.png',
            tag: 'chat-notification'
        });
    }
}

// تغيير نغمة الإشعار
function changeNotificationSound(event) {
    const soundType = event.target.value;

    if (notificationSystem) {
        notificationSystem.updateSoundSettings({ soundType: soundType });
        notificationSystem.playNotificationSound(soundType);
    }

    showNotification(`تم تغيير نغمة الإشعار إلى ${event.target.options[event.target.selectedIndex].text}`, 'info');
}

// تغيير مستوى الصوت
function changeVolume(event) {
    const volume = parseFloat(event.target.value);
    const percentage = Math.round(volume * 100);

    document.getElementById('volumeValue').textContent = `${percentage}%`;

    if (notificationSystem) {
        notificationSystem.updateSoundSettings({ volume: volume });
    }
}

// تبديل وضع عدم الإزعاج
function toggleDoNotDisturb(event) {
    const enabled = event.target.checked;

    if (notificationSystem) {
        notificationSystem.setDoNotDisturb(enabled);
    }

    showNotification(enabled ? 'تم تفعيل وضع عدم الإزعاج' : 'تم إلغاء وضع عدم الإزعاج', 'info');
}

// تبديل ساعات الهدوء
function toggleQuietHours(event) {
    const enabled = event.target.checked;
    const quietHoursSettings = document.querySelector('.quiet-hours-settings');

    if (enabled) {
        quietHoursSettings.style.display = 'block';
    } else {
        quietHoursSettings.style.display = 'none';
    }

    if (notificationSystem) {
        const startTime = document.getElementById('quietHoursStart').value;
        const endTime = document.getElementById('quietHoursEnd').value;

        notificationSystem.updateSoundSettings({
            quietHours: {
                enabled: enabled,
                start: startTime,
                end: endTime
            }
        });
    }

    showNotification(enabled ? 'تم تفعيل ساعات الهدوء' : 'تم إلغاء ساعات الهدوء', 'info');
}

// اختبار الإشعار
function testNotification() {
    if (notificationSystem) {
        notificationSystem.testNotification();
    } else {
        showNotification('اختبار الإشعارات', 'info');
    }
}

// تحديث إعدادات الإشعارات في الواجهة
function updateNotificationSettings() {
    if (notificationSystem) {
        const settings = notificationSystem.settings;

        document.getElementById('desktopNotificationsToggle').checked = settings.desktop;
        document.getElementById('soundNotificationsToggle').checked = settings.sound;
        document.getElementById('notificationSoundSelect').value = settings.soundType;
        document.getElementById('volumeSlider').value = settings.volume;
        document.getElementById('volumeValue').textContent = `${Math.round(settings.volume * 100)}%`;
        document.getElementById('doNotDisturbToggle').checked = settings.doNotDisturb;
        document.getElementById('quietHoursToggle').checked = settings.quietHours.enabled;
        document.getElementById('quietHoursStart').value = settings.quietHours.start;
        document.getElementById('quietHoursEnd').value = settings.quietHours.end;

        // إظهار/إخفاء إعدادات ساعات الهدوء
        const quietHoursSettings = document.querySelector('.quiet-hours-settings');
        if (settings.quietHours.enabled) {
            quietHoursSettings.style.display = 'block';
        } else {
            quietHoursSettings.style.display = 'none';
        }
    }
}

// تهيئة تأثيرات التصميم
function initializeDesignEffects() {
    // إضافة فئات التفاعل للعناصر
    addInteractiveClasses();

    // تهيئة تأثيرات الانتقال
    initializePageTransitions();

    // تهيئة تأثيرات الكتابة
    initializeTypingEffects();

    // تهيئة تأثيرات التمرير
    initializeScrollEffects();
}

// إضافة فئات التفاعل
function addInteractiveClasses() {
    // إضافة فئة interactive-element للأزرار والعناصر التفاعلية
    const interactiveElements = document.querySelectorAll('button, .conversation-item, .user-item, .message');
    interactiveElements.forEach(element => {
        element.classList.add('interactive-element');
    });

    // إضافة تأثير الإضاءة للعناصر المهمة
    const glowElements = document.querySelectorAll('.btn-primary, .send-btn, .new-chat-btn');
    glowElements.forEach(element => {
        element.classList.add('glow-effect');
    });
}

// تهيئة تأثيرات الانتقال
function initializePageTransitions() {
    // إضافة تأثير انتقال للصفحات
    const mainContent = document.querySelector('.app-container');
    if (mainContent) {
        mainContent.classList.add('page-transition');
    }

    // تأثيرات للنوافذ المنبثقة
    const modals = document.querySelectorAll('.modal');
    modals.forEach(modal => {
        const originalDisplay = modal.style.display;

        // تخصيص دالة الإظهار
        modal.show = function() {
            this.style.display = 'flex';
            setTimeout(() => this.classList.add('active'), 10);
        };

        // تخصيص دالة الإخفاء
        modal.hide = function() {
            this.classList.remove('active');
            setTimeout(() => this.style.display = 'none', 300);
        };
    });
}

// تهيئة تأثيرات الكتابة
function initializeTypingEffects() {
    // إنشاء مؤشر الكتابة المحسن
    createTypingIndicator();
}

// إنشاء مؤشر الكتابة
function createTypingIndicator() {
    const typingIndicator = document.createElement('div');
    typingIndicator.id = 'typingIndicator';
    typingIndicator.className = 'typing-indicator';
    typingIndicator.style.display = 'none';

    typingIndicator.innerHTML = `
        <span>يكتب...</span>
        <div class="typing-dots">
            <div class="typing-dot"></div>
            <div class="typing-dot"></div>
            <div class="typing-dot"></div>
        </div>
    `;

    const messagesContainer = document.getElementById('messagesContainer');
    if (messagesContainer) {
        messagesContainer.appendChild(typingIndicator);
    }
}

// إظهار مؤشر الكتابة
function showTypingIndicator(username) {
    const indicator = document.getElementById('typingIndicator');
    if (indicator) {
        indicator.querySelector('span').textContent = `${username} يكتب...`;
        indicator.style.display = 'flex';

        // التمرير لأسفل
        const messagesContainer = document.getElementById('messagesContainer');
        if (messagesContainer) {
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }
    }
}

// إخفاء مؤشر الكتابة
function hideTypingIndicator() {
    const indicator = document.getElementById('typingIndicator');
    if (indicator) {
        indicator.style.display = 'none';
    }
}

// تهيئة تأثيرات التمرير
function initializeScrollEffects() {
    const messagesContainer = document.getElementById('messagesContainer');
    if (!messagesContainer) return;

    // تأثير التمرير السلس
    messagesContainer.classList.add('smooth-scroll');

    // مراقبة التمرير لإضافة تأثيرات
    let scrollTimeout;
    messagesContainer.addEventListener('scroll', () => {
        messagesContainer.classList.add('scrolling');

        clearTimeout(scrollTimeout);
        scrollTimeout = setTimeout(() => {
            messagesContainer.classList.remove('scrolling');
        }, 150);
    });
}

// تحسين عرض الرسائل مع التأثيرات
function displayMessageWithEffects(message) {
    const messageElement = createMessageElement(message);

    // إضافة تأثير الظهور
    messageElement.style.opacity = '0';
    messageElement.style.transform = 'translateY(20px)';

    const messagesContainer = document.getElementById('messagesContainer');
    messagesContainer.appendChild(messageElement);

    // تطبيق التأثير
    setTimeout(() => {
        messageElement.style.transition = 'all 0.3s ease';
        messageElement.style.opacity = '1';
        messageElement.style.transform = 'translateY(0)';
    }, 10);

    // التمرير السلس لأسفل
    setTimeout(() => {
        messagesContainer.scrollTo({
            top: messagesContainer.scrollHeight,
            behavior: 'smooth'
        });
    }, 100);
}

// تحسين تبديل الشريط الجانبي للأجهزة المحمولة
function toggleSidebarMobile() {
    const sidebar = document.querySelector('.sidebar');
    const isMobile = window.innerWidth <= 768;

    if (isMobile) {
        sidebar.classList.toggle('active');

        // إضافة overlay للخلفية
        let overlay = document.querySelector('.sidebar-overlay');
        if (!overlay) {
            overlay = document.createElement('div');
            overlay.className = 'sidebar-overlay';
            overlay.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                z-index: 999;
                opacity: 0;
                transition: opacity 0.3s ease;
                pointer-events: none;
            `;
            document.body.appendChild(overlay);

            overlay.addEventListener('click', () => {
                sidebar.classList.remove('active');
                overlay.style.opacity = '0';
                overlay.style.pointerEvents = 'none';
            });
        }

        if (sidebar.classList.contains('active')) {
            overlay.style.opacity = '1';
            overlay.style.pointerEvents = 'auto';
        } else {
            overlay.style.opacity = '0';
            overlay.style.pointerEvents = 'none';
        }
    }
}

// تحسين استجابة النوافذ
window.addEventListener('resize', () => {
    const sidebar = document.querySelector('.sidebar');
    const isMobile = window.innerWidth <= 768;

    if (!isMobile) {
        sidebar.classList.remove('active');
        const overlay = document.querySelector('.sidebar-overlay');
        if (overlay) {
            overlay.style.opacity = '0';
            overlay.style.pointerEvents = 'none';
        }
    }
});

// معالجة إرسال الرسائل
async function handleSendMessage(e) {
    e.preventDefault();

    const content = messageInput.value.trim();
    if (!content || !currentChatId) return;

    // إضافة الرسالة إلى الواجهة فوراً
    const tempMessage = {
        id: Date.now(),
        content: content,
        sender_id: currentUser.id,
        sender_name: currentUser.username,
        created_at: new Date().toISOString(),
        temp: true
    };

    const messageElement = createMessageElement(tempMessage);
    messagesContainer.appendChild(messageElement);
    scrollToBottom(true); // إجباري للرسائل الجديدة

    // مسح حقل الإدخال
    messageInput.value = '';

    // إرسال الرسالة عبر WebSocket
    if (websocket && websocket.readyState === WebSocket.OPEN) {
        const messageData = {
            type: 'chat_message',
            data: {
                content: content,
                ...(currentChatType === 'group' ?
                    { room_id: currentChatId } :
                    { recipient_id: currentChatId }
                )
            }
        };

        websocket.send(JSON.stringify(messageData));

        // تحديث قائمة المحادثات فوراً
        updateChatListAfterMessage({
            sender_id: currentUser.id,
            recipient_id: currentChatType === 'direct' ? currentChatId : null,
            room_id: currentChatType === 'group' ? currentChatId : null,
            content: content
        });
    } else {
        // إذا لم يكن WebSocket متصل، أرسل عبر HTTP API
        try {
            const endpoint = currentChatType === 'group' ?
                `${API_BASE_URL}/messages/` :
                `${API_BASE_URL}/messages/`;

            const response = await fetch(endpoint, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${authToken}`
                },
                body: JSON.stringify({
                    content: content,
                    ...(currentChatType === 'group' ?
                        { room_id: currentChatId } :
                        { recipient_id: currentChatId }
                    )
                })
            });

            if (response.ok) {
                const newMessage = await response.json();
                // إزالة الرسالة المؤقتة وإضافة الرسالة الحقيقية
                messageElement.remove();
                const realMessageElement = createMessageElement(newMessage);
                messagesContainer.appendChild(realMessageElement);
                scrollToBottom();

                // تحديث قائمة المحادثات
                loadChats();
            } else {
                // إزالة الرسالة المؤقتة في حالة الفشل
                messageElement.remove();
                showNotification('فشل في إرسال الرسالة', 'error');
            }
        } catch (error) {
            console.error('Error sending message:', error);
            messageElement.remove();
            showNotification('خطأ في إرسال الرسالة', 'error');
        }
    }
}

// معالجة مؤشر الكتابة
function handleTyping() {
    if (!currentChatId || !websocket || websocket.readyState !== WebSocket.OPEN) return;

    if (!isTyping) {
        isTyping = true;

        const typingData = {
            type: 'typing',
            data: {
                is_typing: true,
                recipient_id: currentChatId
            }
        };

        websocket.send(JSON.stringify(typingData));
    }

    // إلغاء المؤقت السابق
    clearTimeout(typingTimeout);

    // تعيين مؤقت جديد لإيقاف مؤشر الكتابة
    typingTimeout = setTimeout(() => {
        if (isTyping) {
            isTyping = false;

            const typingData = {
                type: 'typing',
                data: {
                    is_typing: false,
                    recipient_id: currentChatId
                }
            };

            websocket.send(JSON.stringify(typingData));
        }
    }, 2000);
}

// معالجة مؤشر الكتابة الواردة
function handleTypingIndicator(data) {
    const typingIndicator = document.getElementById('typingIndicator');

    if (data.is_typing && data.sender_id !== currentUser.id) {
        typingIndicator.style.display = 'flex';
        scrollToBottom();
    } else {
        typingIndicator.style.display = 'none';
    }
}

// عرض نافذة المحادثة الجديدة
function showNewChatModal() {
    newChatModal.classList.add('active');
    loadUsers();
}

// إخفاء نافذة المحادثة الجديدة
function hideNewChatModal() {
    newChatModal.classList.remove('active');
    document.getElementById('userSearchInput').value = '';
    usersList.innerHTML = '';
}

// تحميل المستخدمين
async function loadUsers() {
    try {
        const response = await fetch(`${API_BASE_URL}/users/?size=100`, {
            headers: {
                'Authorization': `Bearer ${authToken}`
            }
        });

        if (response.ok) {
            const data = await response.json();
            // التحقق من نوع البيانات المُعادة
            let users = [];
            if (Array.isArray(data)) {
                users = data;
            } else if (data.items && Array.isArray(data.items)) {
                users = data.items;
            } else {
                console.error('Unexpected users data format:', data);
                showNotification('خطأ في تحميل قائمة المستخدمين', 'error');
                return;
            }

            // فلترة المستخدم الحالي
            const filteredUsers = users.filter(user => user.id !== currentUser?.id);
            displayUsers(filteredUsers);
        } else {
            console.error('Failed to load users:', response.status);
            showNotification('فشل في تحميل قائمة المستخدمين', 'error');
        }
    } catch (error) {
        console.error('Error loading users:', error);
        showNotification('خطأ في الاتصال بالخادم', 'error');
    }
}

// عرض المستخدمين
function displayUsers(users) {
    usersList.innerHTML = '';

    if (users.length === 0) {
        usersList.innerHTML = `
            <div style="text-align: center; padding: 20px; color: #666;">
                لا توجد مستخدمين
            </div>
        `;
        return;
    }

    users.forEach(user => {
        const userItem = createUserItem(user);
        usersList.appendChild(userItem);
    });
}

// إنشاء عنصر مستخدم
function createUserItem(user) {
    const userItem = document.createElement('div');
    userItem.className = 'user-item';

    userItem.innerHTML = `
        <div class="user-item-avatar">
            ${user.username.charAt(0).toUpperCase()}
        </div>
        <div class="user-item-info">
            <div class="user-item-name">${user.username}</div>
            <div class="user-item-status ${user.is_online ? 'online' : 'offline'}">
                ${user.is_online ? 'متصل' : 'غير متصل'}
            </div>
        </div>
    `;

    userItem.addEventListener('click', () => startChatWithUser(user));

    return userItem;
}

// بدء محادثة مع مستخدم
async function startChatWithUser(user) {
    try {
        // إنشاء محادثة جديدة أو الحصول على المحادثة الموجودة
        const conversation = {
            id: user.id,
            name: user.username,
            last_message: null,
            unread_count: 0
        };

        hideNewChatModal();
        await openChat(conversation);

        // تحديث قائمة المحادثات فوراً
        loadChats();

    } catch (error) {
        console.error('Error starting chat:', error);
        showNotification('خطأ في بدء المحادثة', 'error');
    }
}

// البحث عن المستخدمين
function searchUsers() {
    const searchTerm = document.getElementById('userSearchInput').value.toLowerCase();
    const userItems = usersList.querySelectorAll('.user-item');

    userItems.forEach(item => {
        const userName = item.querySelector('.user-item-name').textContent.toLowerCase();
        if (userName.includes(searchTerm)) {
            item.style.display = 'flex';
        } else {
            item.style.display = 'none';
        }
    });
}

// عرض رسالة جديدة
function displayMessage(message) {
    console.log('Displaying message:', message); // للتشخيص
    console.log('Current chat ID:', currentChatId, 'Current chat type:', currentChatType); // للتشخيص

    // تحديد ما إذا كانت الرسالة للمحادثة الحالية
    let isCurrentChat = false;

    if (currentChatType === 'group' && message.room_id === currentChatId) {
        isCurrentChat = true;
    } else if (currentChatType === 'direct' &&
               (message.sender_id === currentChatId || message.recipient_id === currentChatId)) {
        isCurrentChat = true;
    }

    // إذا كانت الرسالة للمحادثة الحالية، اعرضها فوراً
    if (isCurrentChat) {
        const messagesContainer = document.getElementById('messagesContainer');
        if (messagesContainer) {
            const messageElement = createMessageElement(message);
            messagesContainer.appendChild(messageElement);
            scrollToBottom();
        }
    }
}

// تحديث حالة المستخدم
function updateUserStatus(data) {
    // تحديث حالة المستخدم في الواجهة
    const userItems = document.querySelectorAll('.user-item');
    userItems.forEach(item => {
        const userName = item.querySelector('.user-item-name').textContent;
        if (userName === data.username) {
            const statusElement = item.querySelector('.user-item-status');
            statusElement.textContent = data.is_online ? 'متصل' : 'غير متصل';
            statusElement.className = `user-item-status ${data.is_online ? 'online' : 'offline'}`;
        }
    });
}

// التمرير إلى أسفل
function scrollToBottom(force = false) {
    if (messagesContainer) {
        // التحقق من أن المستخدم في الأسفل أو إجبار التمرير
        const isAtBottom = messagesContainer.scrollTop + messagesContainer.clientHeight >= messagesContainer.scrollHeight - 50;

        if (force || isAtBottom) {
            setTimeout(() => {
                messagesContainer.scrollTop = messagesContainer.scrollHeight;
            }, 100);
        }
    }
}

// تنسيق الوقت
function formatTime(date) {
    return date.toLocaleTimeString('ar-SA', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: true
    });
}

// تنسيق الوقت النسبي
function formatTimeAgo(date) {
    const now = new Date();
    const diffInSeconds = Math.floor((now - date) / 1000);

    if (diffInSeconds < 60) {
        return 'الآن';
    } else if (diffInSeconds < 3600) {
        const minutes = Math.floor(diffInSeconds / 60);
        return `منذ ${minutes} دقيقة`;
    } else if (diffInSeconds < 86400) {
        const hours = Math.floor(diffInSeconds / 3600);
        return `منذ ${hours} ساعة`;
    } else {
        const days = Math.floor(diffInSeconds / 86400);
        return `منذ ${days} يوم`;
    }
}

// تعديل رسالة
async function editMessage(messageId) {
    const messageElement = document.querySelector(`[data-message-id="${messageId}"]`);
    if (!messageElement) return;

    const messageText = messageElement.querySelector('.message-text');
    if (!messageText) return;

    const currentText = messageText.textContent;
    const newText = prompt('تعديل الرسالة:', currentText);

    if (newText && newText !== currentText) {
        try {
            const response = await fetch(`${API_BASE_URL}/messages/${messageId}`, {
                method: 'PUT',
                headers: {
                    'Authorization': `Bearer ${authToken}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ content: newText })
            });

            if (response.ok) {
                const updatedMessage = await response.json();
                messageText.textContent = updatedMessage.content;

                // إضافة علامة التعديل
                const timeElement = messageElement.querySelector('.message-time');
                if (timeElement && !timeElement.textContent.includes('(معدلة)')) {
                    timeElement.textContent += ' (معدلة)';
                }

                showNotification('تم تعديل الرسالة بنجاح', 'success');
            } else {
                showNotification('فشل في تعديل الرسالة', 'error');
            }
        } catch (error) {
            console.error('Error editing message:', error);
            showNotification('خطأ في تعديل الرسالة', 'error');
        }
    }
}

// حذف رسالة
async function deleteMessage(messageId) {
    if (!confirm('هل أنت متأكد من حذف هذه الرسالة؟')) {
        return;
    }

    try {
        const response = await fetch(`${API_BASE_URL}/messages/${messageId}`, {
            method: 'DELETE',
            headers: {
                'Authorization': `Bearer ${authToken}`
            }
        });

        if (response.ok) {
            const messageElement = document.querySelector(`[data-message-id="${messageId}"]`);
            if (messageElement) {
                messageElement.style.transition = 'all 0.3s ease';
                messageElement.style.opacity = '0';
                messageElement.style.transform = 'translateX(-100%)';

                setTimeout(() => {
                    messageElement.remove();
                }, 300);
            }

            showNotification('تم حذف الرسالة بنجاح', 'success');
        } else {
            showNotification('فشل في حذف الرسالة', 'error');
        }
    } catch (error) {
        console.error('Error deleting message:', error);
        showNotification('خطأ في حذف الرسالة', 'error');
    }
}

// إظهار/إخفاء قائمة خيارات المحادثة
function toggleChatOptions(event, chatId) {
    event.stopPropagation(); // منع فتح المحادثة

    const menu = document.getElementById(`chatOptions${chatId}`);
    const allMenus = document.querySelectorAll('.chat-options-menu');

    // إخفاء جميع القوائم الأخرى
    allMenus.forEach(m => {
        if (m !== menu) m.style.display = 'none';
    });

    // تبديل القائمة الحالية
    menu.style.display = menu.style.display === 'block' ? 'none' : 'block';
}

// حذف محادثة كاملة
async function deleteChatConversation(chatId, chatName) {
    if (!confirm(`هل أنت متأكد من حذف محادثة "${chatName}" نهائياً؟\nسيتم حذف جميع الرسائل ولا يمكن التراجع عن هذا الإجراء.`)) {
        return;
    }

    try {
        const response = await fetch(`${API_BASE_URL}/messages/conversation/${chatId}`, {
            method: 'DELETE',
            headers: {
                'Authorization': `Bearer ${authToken}`
            }
        });

        if (response.ok) {
            // إزالة المحادثة من القائمة
            const chatElement = document.querySelector(`[data-chat-id="${chatId}"]`);
            if (chatElement) {
                chatElement.style.transition = 'all 0.3s ease';
                chatElement.style.opacity = '0';
                chatElement.style.transform = 'translateX(-100%)';

                setTimeout(() => {
                    chatElement.remove();
                }, 300);
            }

            // إذا كانت المحادثة المحذوفة مفتوحة، أغلقها
            if (currentChatId == chatId) {
                const welcomeScreen = document.getElementById('welcomeScreen');
                const chatWindow = document.getElementById('chatWindow');
                if (welcomeScreen) welcomeScreen.style.display = 'flex';
                if (chatWindow) chatWindow.style.display = 'none';
                currentChatId = null;
            }

            showNotification('تم حذف المحادثة بنجاح', 'success');
        } else {
            showNotification('فشل في حذف المحادثة', 'error');
        }
    } catch (error) {
        console.error('Error deleting conversation:', error);
        showNotification('خطأ في حذف المحادثة', 'error');
    }
}

// أرشفة محادثة (مؤقتاً - سيتم تطويرها لاحقاً)
function archiveConversation(chatId) {
    showNotification('ميزة الأرشفة قريباً!', 'info');
}

// إخفاء قوائم الخيارات عند النقر خارجها
document.addEventListener('click', () => {
    const allMenus = document.querySelectorAll('.chat-options-menu');
    allMenus.forEach(menu => menu.style.display = 'none');
});

// إدارة البروفايل
function initializeProfile() {
    const profileBtn = document.getElementById('profileBtn');
    const profileModal = document.getElementById('profileModal');
    const closeProfileModal = document.getElementById('closeProfileModal');
    const profileForm = document.getElementById('profileForm');
    const changePasswordBtn = document.getElementById('changePasswordBtn');
    const changeAvatarBtn = document.getElementById('changeAvatarBtn');
    const avatarInput = document.getElementById('avatarInput');

    if (profileBtn) {
        profileBtn.addEventListener('click', showProfile);
    }

    if (closeProfileModal) {
        closeProfileModal.addEventListener('click', () => {
            profileModal.style.display = 'none';
        });
    }

    if (profileForm) {
        profileForm.addEventListener('submit', updateProfile);
    }

    if (changePasswordBtn) {
        changePasswordBtn.addEventListener('click', showChangePasswordModal);
    }

    if (changeAvatarBtn) {
        changeAvatarBtn.addEventListener('click', () => {
            avatarInput.click();
        });
    }

    if (avatarInput) {
        avatarInput.addEventListener('change', handleAvatarChange);
    }
}

// عرض البروفايل
async function showProfile() {
    const profileModal = document.getElementById('profileModal');

    if (currentUser) {
        // ملء البيانات الحالية
        document.getElementById('profileUsername').value = currentUser.username || '';
        document.getElementById('profileEmail').value = currentUser.email || '';
        document.getElementById('profileBio').value = currentUser.bio || '';

        // عرض الصورة الشخصية
        const avatarImg = document.getElementById('profileAvatarImg');
        const avatarText = document.getElementById('profileAvatarText');

        if (currentUser.avatar_url) {
            avatarImg.src = currentUser.avatar_url;
            avatarImg.style.display = 'block';
            avatarText.style.display = 'none';
        } else {
            avatarImg.style.display = 'none';
            avatarText.style.display = 'flex';
            avatarText.textContent = currentUser.username.charAt(0).toUpperCase();
        }
    }

    profileModal.style.display = 'flex';
}

// تحديث البروفايل
async function updateProfile(event) {
    event.preventDefault();

    const formData = new FormData(event.target);
    const profileData = {
        email: formData.get('email'),
        bio: formData.get('bio')
    };

    try {
        const response = await fetch(`${API_BASE_URL}/auth/profile`, {
            method: 'PUT',
            headers: {
                'Authorization': `Bearer ${authToken}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(profileData)
        });

        if (response.ok) {
            const updatedUser = await response.json();
            currentUser = { ...currentUser, ...updatedUser };
            localStorage.setItem('currentUser', JSON.stringify(currentUser));

            showNotification('تم تحديث البروفايل بنجاح', 'success');
        } else {
            showNotification('فشل في تحديث البروفايل', 'error');
        }
    } catch (error) {
        console.error('Error updating profile:', error);
        showNotification('خطأ في تحديث البروفايل', 'error');
    }
}

// تغيير الصورة الشخصية
async function handleAvatarChange(event) {
    const file = event.target.files[0];
    if (!file) return;

    // التحقق من نوع الملف
    if (!file.type.startsWith('image/')) {
        showNotification('يرجى اختيار ملف صورة صحيح', 'error');
        return;
    }

    // التحقق من حجم الملف (2MB max)
    if (file.size > 2 * 1024 * 1024) {
        showNotification('حجم الصورة يجب أن يكون أقل من 2 ميجابايت', 'error');
        return;
    }

    const formData = new FormData();
    formData.append('avatar', file);

    try {
        const response = await fetch(`${API_BASE_URL}/auth/avatar`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${authToken}`
            },
            body: formData
        });

        if (response.ok) {
            const result = await response.json();
            currentUser.avatar_url = result.avatar_url;
            localStorage.setItem('currentUser', JSON.stringify(currentUser));

            // تحديث الصورة في الواجهة
            const avatarImg = document.getElementById('profileAvatarImg');
            const avatarText = document.getElementById('profileAvatarText');

            avatarImg.src = result.avatar_url;
            avatarImg.style.display = 'block';
            avatarText.style.display = 'none';

            showNotification('تم تحديث الصورة الشخصية بنجاح', 'success');
        } else {
            showNotification('فشل في تحديث الصورة الشخصية', 'error');
        }
    } catch (error) {
        console.error('Error uploading avatar:', error);
        showNotification('خطأ في رفع الصورة', 'error');
    }
}

// عرض نافذة تغيير كلمة المرور
function showChangePasswordModal() {
    showNotification('ميزة تغيير كلمة المرور قريباً!', 'info');
}

// 😀 نظام الرموز التعبيرية الرائع
const emojiData = {
    recent: [],
    smileys: ['😀', '😃', '😄', '😁', '😆', '😅', '🤣', '😂', '🙂', '🙃', '😉', '😊', '😇', '🥰', '😍', '🤩', '😘', '😗', '😚', '😙', '😋', '😛', '😜', '🤪', '😝', '🤑', '🤗', '🤭', '🤫', '🤔', '🤐', '🤨', '😐', '😑', '😶', '😏', '😒', '🙄', '😬', '🤥'],
    people: ['👶', '🧒', '👦', '👧', '🧑', '👱', '👨', '🧔', '👩', '🧓', '👴', '👵', '🙍', '🙎', '🙅', '🙆', '💁', '🙋', '🧏', '🙇', '🤦', '🤷', '👮', '🕵️', '💂', '👷', '🤴', '👸', '👳', '👲', '🧕', '🤵', '👰', '🤰', '🤱', '👼', '🎅', '🤶', '🦸', '🦹'],
    nature: ['🐶', '🐱', '🐭', '🐹', '🐰', '🦊', '🐻', '🐼', '🐨', '🐯', '🦁', '🐮', '🐷', '🐽', '🐸', '🐵', '🙈', '🙉', '🙊', '🐒', '🐔', '🐧', '🐦', '🐤', '🐣', '🐥', '🦆', '🦅', '🦉', '🦇', '🐺', '🐗', '🐴', '🦄', '🐝', '🐛', '🦋', '🐌', '🐞', '🐜'],
    food: ['🍎', '🍐', '🍊', '🍋', '🍌', '🍉', '🍇', '🍓', '🍈', '🍒', '🍑', '🥭', '🍍', '🥥', '🥝', '🍅', '🍆', '🥑', '🥦', '🥬', '🥒', '🌶️', '🌽', '🥕', '🧄', '🧅', '🥔', '🍠', '🥐', '🥖', '🍞', '🥨', '🥯', '🧀', '🥚', '🍳', '🧈', '🥞', '🧇', '🥓'],
    activities: ['⚽', '🏀', '🏈', '⚾', '🥎', '🎾', '🏐', '🏉', '🥏', '🎱', '🪀', '🏓', '🏸', '🏒', '🏑', '🥍', '🏏', '🪃', '🥅', '⛳', '🪁', '🏹', '🎣', '🤿', '🥊', '🥋', '🎽', '🛹', '🛷', '⛸️', '🥌', '🎿', '⛷️', '🏂', '🪂', '🏋️', '🤼', '🤸', '⛹️', '🤺'],
    travel: ['🚗', '🚕', '🚙', '🚌', '🚎', '🏎️', '🚓', '🚑', '🚒', '🚐', '🛻', '🚚', '🚛', '🚜', '🏍️', '🛵', '🚲', '🛴', '🛹', '🛼', '🚁', '🛸', '✈️', '🛩️', '🛫', '🛬', '🪂', '💺', '🚀', '🛰️', '🚢', '⛵', '🚤', '🛥️', '🛳️', '⛴️', '🚂', '🚃', '🚄', '🚅'],
    objects: ['⌚', '📱', '📲', '💻', '⌨️', '🖥️', '🖨️', '🖱️', '🖲️', '🕹️', '🗜️', '💽', '💾', '💿', '📀', '📼', '📷', '📸', '📹', '🎥', '📽️', '🎞️', '📞', '☎️', '📟', '📠', '📺', '📻', '🎙️', '🎚️', '🎛️', '🧭', '⏱️', '⏲️', '⏰', '🕰️', '⌛', '⏳', '📡', '🔋'],
    symbols: ['❤️', '🧡', '💛', '💚', '💙', '💜', '🖤', '🤍', '🤎', '💔', '❣️', '💕', '💞', '💓', '💗', '💖', '💘', '💝', '💟', '☮️', '✝️', '☪️', '🕉️', '☸️', '✡️', '🔯', '🕎', '☯️', '☦️', '🛐', '⛎', '♈', '♉', '♊', '♋', '♌', '♍', '♎', '♏'],
    flags: ['🏁', '🚩', '🎌', '🏴', '🏳️', '🏳️‍🌈', '🏳️‍⚧️', '🏴‍☠️', '🇦🇫', '🇦🇽', '🇦🇱', '🇩🇿', '🇦🇸', '🇦🇩', '🇦🇴', '🇦🇮', '🇦🇶', '🇦🇬', '🇦🇷', '🇦🇲', '🇦🇼', '🇦🇺', '🇦🇹', '🇦🇿', '🇧🇸', '🇧🇭', '🇧🇩', '🇧🇧', '🇧🇾', '🇧🇪', '🇧🇿', '🇧🇯', '🇧🇲', '🇧🇹', '🇧🇴', '🇧🇦', '🇧🇼', '🇧🇷', '🇮🇴']
};

let currentEmojiCategory = 'recent';

// تهيئة نظام الرموز التعبيرية
function initializeEmojiSystem() {
    const emojiBtn = document.getElementById('emojiBtn');
    const emojiPanel = document.getElementById('emojiPanel');
    const closeEmojiPanel = document.getElementById('closeEmojiPanel');
    const emojiTabs = document.querySelectorAll('.emoji-tab');
    const emojiSearch = document.getElementById('emojiSearch');

    if (emojiBtn) {
        emojiBtn.addEventListener('click', toggleEmojiPanel);
    }

    if (closeEmojiPanel) {
        closeEmojiPanel.addEventListener('click', hideEmojiPanel);
    }

    emojiTabs.forEach(tab => {
        tab.addEventListener('click', () => {
            const category = tab.dataset.category;
            switchEmojiCategory(category);
        });
    });

    if (emojiSearch) {
        emojiSearch.addEventListener('input', searchEmojis);
    }

    // إخفاء اللوحة عند النقر خارجها
    document.addEventListener('click', (e) => {
        if (!emojiPanel.contains(e.target) && !emojiBtn.contains(e.target)) {
            hideEmojiPanel();
        }
    });

    // تحميل الرموز المستخدمة مؤخراً
    loadRecentEmojis();

    // عرض الفئة الافتراضية
    switchEmojiCategory('smileys');
}

// تبديل عرض لوحة الرموز التعبيرية
function toggleEmojiPanel() {
    const emojiPanel = document.getElementById('emojiPanel');
    if (emojiPanel.style.display === 'flex') {
        hideEmojiPanel();
    } else {
        showEmojiPanel();
    }
}

// إظهار لوحة الرموز التعبيرية
function showEmojiPanel() {
    const emojiPanel = document.getElementById('emojiPanel');
    emojiPanel.style.display = 'flex';
    console.log('😀 Emoji panel opened');
}

// إخفاء لوحة الرموز التعبيرية
function hideEmojiPanel() {
    const emojiPanel = document.getElementById('emojiPanel');
    emojiPanel.style.display = 'none';
}

// تبديل فئة الرموز التعبيرية
function switchEmojiCategory(category) {
    currentEmojiCategory = category;

    // تحديث التبويبات
    document.querySelectorAll('.emoji-tab').forEach(tab => {
        tab.classList.remove('active');
    });
    document.querySelector(`[data-category="${category}"]`).classList.add('active');

    // عرض الرموز
    displayEmojis(emojiData[category] || []);
}

// عرض الرموز التعبيرية
function displayEmojis(emojis) {
    const emojiContent = document.getElementById('emojiContent');
    emojiContent.innerHTML = '';

    emojis.forEach(emoji => {
        const emojiItem = document.createElement('div');
        emojiItem.className = 'emoji-item';
        emojiItem.textContent = emoji;
        emojiItem.title = emoji;

        emojiItem.addEventListener('click', () => {
            insertEmoji(emoji);
        });

        emojiContent.appendChild(emojiItem);
    });
}

// إدراج رمز تعبيري في حقل النص
function insertEmoji(emoji) {
    const messageInput = document.getElementById('messageInput');
    if (messageInput) {
        const currentValue = messageInput.value;
        const cursorPosition = messageInput.selectionStart;

        const newValue = currentValue.slice(0, cursorPosition) + emoji + currentValue.slice(cursorPosition);
        messageInput.value = newValue;

        // تحريك المؤشر بعد الرمز التعبيري
        messageInput.focus();
        messageInput.setSelectionRange(cursorPosition + emoji.length, cursorPosition + emoji.length);

        // إضافة للمستخدمة مؤخراً
        addToRecentEmojis(emoji);

        console.log('😀 Inserted emoji:', emoji);
    }
}

// البحث في الرموز التعبيرية
function searchEmojis() {
    const searchTerm = document.getElementById('emojiSearch').value.toLowerCase();

    if (searchTerm === '') {
        switchEmojiCategory(currentEmojiCategory);
        return;
    }

    // البحث في جميع الفئات
    const allEmojis = Object.values(emojiData).flat();
    const filteredEmojis = allEmojis.filter(emoji => {
        // يمكن إضافة منطق بحث أكثر تطوراً هنا
        return true; // مؤقتاً نعرض كل الرموز
    });

    displayEmojis(filteredEmojis.slice(0, 64)); // عرض أول 64 رمز
}

// إضافة رمز للمستخدمة مؤخراً
function addToRecentEmojis(emoji) {
    let recent = JSON.parse(localStorage.getItem('recentEmojis') || '[]');

    // إزالة الرمز إذا كان موجوداً
    recent = recent.filter(e => e !== emoji);

    // إضافة في المقدمة
    recent.unshift(emoji);

    // الاحتفاظ بآخر 32 رمز
    recent = recent.slice(0, 32);

    localStorage.setItem('recentEmojis', JSON.stringify(recent));
    emojiData.recent = recent;
}

// تحميل الرموز المستخدمة مؤخراً
function loadRecentEmojis() {
    const recent = JSON.parse(localStorage.getItem('recentEmojis') || '[]');
    emojiData.recent = recent;
}

// 📁 نظام رفع الملفات الرائع
function initializeFileSystem() {
    const fileBtn = document.getElementById('fileBtn');
    const fileInput = document.createElement('input');
    fileInput.type = 'file';
    fileInput.multiple = true;
    fileInput.accept = 'image/*,video/*,audio/*,.pdf,.doc,.docx,.txt,.zip,.rar';
    fileInput.style.display = 'none';
    document.body.appendChild(fileInput);

    if (fileBtn) {
        fileBtn.addEventListener('click', () => {
            fileInput.click();
        });
    }

    fileInput.addEventListener('change', handleFileSelection);

    // دعم السحب والإفلات
    setupDragAndDrop();
}

// معالجة اختيار الملفات
function handleFileSelection(event) {
    const files = Array.from(event.target.files);
    files.forEach(file => {
        uploadFile(file);
    });

    // إعادة تعيين قيمة الإدخال
    event.target.value = '';
}

// رفع ملف
async function uploadFile(file) {
    if (!currentChatId) {
        showNotification('يرجى اختيار محادثة أولاً', 'warning');
        return;
    }

    // التحقق من حجم الملف (10MB max)
    if (file.size > 10 * 1024 * 1024) {
        showNotification('حجم الملف يجب أن يكون أقل من 10 ميجابايت', 'error');
        return;
    }

    // إنشاء رسالة مؤقتة
    const tempMessage = {
        id: 'temp_' + Date.now(),
        content: `📎 جاري رفع ${file.name}...`,
        sender_id: currentUser.id,
        sender_name: 'أنت',
        created_at: new Date().toISOString(),
        status: 'sending',
        file_info: {
            type: getFileType(file),
            filename: file.name,
            size: file.size,
            uploading: true
        }
    };

    // عرض الرسالة المؤقتة
    const messageElement = createMessageElement(tempMessage);
    messagesContainer.appendChild(messageElement);
    scrollToBottom(true);

    try {
        const formData = new FormData();
        formData.append('file', file);
        formData.append('recipient_id', currentChatId);

        const response = await fetch(`${API_BASE_URL}/messages/upload`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${authToken}`
            },
            body: formData
        });

        if (response.ok) {
            const result = await response.json();

            // تحديث الرسالة المؤقتة
            tempMessage.content = `📎 ${file.name}`;
            tempMessage.status = 'sent';
            tempMessage.file_info.uploading = false;
            tempMessage.file_info.url = result.file_url;

            // إعادة إنشاء عنصر الرسالة
            const newMessageElement = createMessageElement(tempMessage);
            messageElement.replaceWith(newMessageElement);

            showNotification('تم رفع الملف بنجاح!', 'success');
        } else {
            throw new Error('فشل في رفع الملف');
        }
    } catch (error) {
        console.error('Error uploading file:', error);
        messageElement.remove();
        showNotification('فشل في رفع الملف', 'error');
    }
}

// تحديد نوع الملف
function getFileType(file) {
    const type = file.type;
    if (type.startsWith('image/')) return 'image';
    if (type.startsWith('video/')) return 'video';
    if (type.startsWith('audio/')) return 'audio';
    if (type === 'application/pdf') return 'pdf';
    if (type.includes('document') || type.includes('text')) return 'document';
    return 'file';
}

// إعداد السحب والإفلات
function setupDragAndDrop() {
    const dropZone = messagesContainer;

    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
        dropZone.addEventListener(eventName, preventDefaults, false);
    });

    function preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }

    ['dragenter', 'dragover'].forEach(eventName => {
        dropZone.addEventListener(eventName, highlight, false);
    });

    ['dragleave', 'drop'].forEach(eventName => {
        dropZone.addEventListener(eventName, unhighlight, false);
    });

    function highlight(e) {
        dropZone.classList.add('drag-over');
    }

    function unhighlight(e) {
        dropZone.classList.remove('drag-over');
    }

    dropZone.addEventListener('drop', handleDrop, false);

    function handleDrop(e) {
        const dt = e.dataTransfer;
        const files = dt.files;

        Array.from(files).forEach(file => {
            uploadFile(file);
        });
    }
}

// إنشاء معاينة الملف
function createFilePreview(message) {
    const fileInfo = message.file_info;
    if (!fileInfo) return message.content;

    const fileIcon = getFileIcon(fileInfo.type);
    const fileSize = formatFileSize(fileInfo.size);

    if (fileInfo.uploading) {
        return `
            <div class="file-message uploading">
                <div class="file-icon">📤</div>
                <div class="file-info">
                    <div class="file-name">${fileInfo.filename}</div>
                    <div class="file-size">جاري الرفع... ${fileSize}</div>
                    <div class="upload-progress">
                        <div class="progress-bar"></div>
                    </div>
                </div>
            </div>
        `;
    }

    if (fileInfo.type === 'image') {
        return `
            <div class="image-message">
                <img src="${fileInfo.url}" alt="${fileInfo.filename}" onclick="openImageViewer('${fileInfo.url}')">
                <div class="image-info">
                    <span class="image-name">${fileInfo.filename}</span>
                    <span class="image-size">${fileSize}</span>
                </div>
            </div>
        `;
    }

    return `
        <div class="file-message">
            <div class="file-icon">${fileIcon}</div>
            <div class="file-info">
                <div class="file-name">${fileInfo.filename}</div>
                <div class="file-size">${fileSize}</div>
            </div>
            <a href="${fileInfo.url}" download="${fileInfo.filename}" class="file-download">
                <i class="fas fa-download"></i>
            </a>
        </div>
    `;
}

// الحصول على أيقونة الملف
function getFileIcon(type) {
    const icons = {
        image: '🖼️',
        video: '🎥',
        audio: '🎵',
        pdf: '📄',
        document: '📝',
        file: '📎'
    };
    return icons[type] || '📎';
}

// تنسيق حجم الملف
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// فتح عارض الصور
function openImageViewer(imageUrl) {
    const viewer = document.createElement('div');
    viewer.className = 'image-viewer';
    viewer.innerHTML = `
        <div class="image-viewer-backdrop" onclick="this.parentElement.remove()">
            <div class="image-viewer-content">
                <img src="${imageUrl}" alt="صورة">
                <button class="image-viewer-close" onclick="this.parentElement.parentElement.remove()">×</button>
            </div>
        </div>
    `;
    document.body.appendChild(viewer);
}

// 🎵 نظام الرسائل الصوتية
let mediaRecorder = null;
let audioChunks = [];
let isRecording = false;

function initializeVoiceSystem() {
    const voiceBtn = document.getElementById('voiceBtn');

    if (voiceBtn) {
        voiceBtn.addEventListener('mousedown', startRecording);
        voiceBtn.addEventListener('mouseup', stopRecording);
        voiceBtn.addEventListener('mouseleave', stopRecording);

        // دعم اللمس للهواتف
        voiceBtn.addEventListener('touchstart', startRecording);
        voiceBtn.addEventListener('touchend', stopRecording);
    }
}

// بدء التسجيل
async function startRecording() {
    if (isRecording) return;

    try {
        const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
        mediaRecorder = new MediaRecorder(stream);
        audioChunks = [];

        mediaRecorder.ondataavailable = (event) => {
            audioChunks.push(event.data);
        };

        mediaRecorder.onstop = () => {
            const audioBlob = new Blob(audioChunks, { type: 'audio/wav' });
            sendVoiceMessage(audioBlob);

            // إيقاف الميكروفون
            stream.getTracks().forEach(track => track.stop());
        };

        mediaRecorder.start();
        isRecording = true;

        // تحديث واجهة الزر
        const voiceBtn = document.getElementById('voiceBtn');
        voiceBtn.classList.add('recording');
        voiceBtn.innerHTML = '<i class="fas fa-stop"></i>';

        showNotification('🎤 جاري التسجيل... اتركه للإرسال', 'info');

    } catch (error) {
        console.error('Error accessing microphone:', error);
        showNotification('لا يمكن الوصول للميكروفون', 'error');
    }
}

// إيقاف التسجيل
function stopRecording() {
    if (!isRecording || !mediaRecorder) return;

    mediaRecorder.stop();
    isRecording = false;

    // إعادة تعيين واجهة الزر
    const voiceBtn = document.getElementById('voiceBtn');
    voiceBtn.classList.remove('recording');
    voiceBtn.innerHTML = '<i class="fas fa-microphone"></i>';
}

// إرسال رسالة صوتية
async function sendVoiceMessage(audioBlob) {
    if (!currentChatId) {
        showNotification('يرجى اختيار محادثة أولاً', 'warning');
        return;
    }

    // إنشاء رسالة مؤقتة
    const tempMessage = {
        id: 'temp_voice_' + Date.now(),
        content: '🎵 رسالة صوتية',
        sender_id: currentUser.id,
        sender_name: 'أنت',
        created_at: new Date().toISOString(),
        status: 'sending',
        content_type: 'audio',
        audio_info: {
            duration: 0,
            uploading: true
        }
    };

    // عرض الرسالة المؤقتة
    const messageElement = createMessageElement(tempMessage);
    messagesContainer.appendChild(messageElement);
    scrollToBottom(true);

    try {
        const formData = new FormData();
        formData.append('file', audioBlob, 'voice_message.wav');
        formData.append('recipient_id', currentChatId);
        formData.append('message_type', 'voice');

        const response = await fetch(`${API_BASE_URL}/messages/upload`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${authToken}`
            },
            body: formData
        });

        if (response.ok) {
            const result = await response.json();

            // تحديث الرسالة المؤقتة
            tempMessage.status = 'sent';
            tempMessage.audio_info.uploading = false;
            tempMessage.audio_info.url = result.file_url;

            // إعادة إنشاء عنصر الرسالة
            const newMessageElement = createMessageElement(tempMessage);
            messageElement.replaceWith(newMessageElement);

            showNotification('تم إرسال الرسالة الصوتية!', 'success');
        } else {
            throw new Error('فشل في إرسال الرسالة الصوتية');
        }
    } catch (error) {
        console.error('Error sending voice message:', error);
        messageElement.remove();
        showNotification('فشل في إرسال الرسالة الصوتية', 'error');
    }
}

// إنشاء عنصر الرسالة الصوتية
function createAudioMessage(message) {
    const audioInfo = message.audio_info;

    if (audioInfo && audioInfo.uploading) {
        return `
            <div class="audio-message uploading">
                <div class="audio-icon">🎤</div>
                <div class="audio-info">
                    <div class="audio-text">جاري رفع الرسالة الصوتية...</div>
                    <div class="upload-progress">
                        <div class="progress-bar"></div>
                    </div>
                </div>
            </div>
        `;
    }

    return `
        <div class="audio-message">
            <button class="audio-play-btn" onclick="toggleAudioPlayback('${audioInfo?.url || ''}')">
                <i class="fas fa-play"></i>
            </button>
            <div class="audio-info">
                <div class="audio-waveform">
                    <div class="waveform-bar"></div>
                    <div class="waveform-bar"></div>
                    <div class="waveform-bar"></div>
                    <div class="waveform-bar"></div>
                    <div class="waveform-bar"></div>
                </div>
                <div class="audio-duration">${formatAudioDuration(audioInfo?.duration || 0)}</div>
            </div>
        </div>
    `;
}

// تشغيل/إيقاف الصوت
function toggleAudioPlayback(audioUrl) {
    // TODO: تنفيذ تشغيل الصوت
    console.log('Playing audio:', audioUrl);
    showNotification('تشغيل الصوت قريباً!', 'info');
}

// تنسيق مدة الصوت
function formatAudioDuration(seconds) {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
}

// 🔍 نظام البحث المتقدم
function initializeAdvancedSearch() {
    const searchInput = document.getElementById('chatSearch');

    if (searchInput) {
        // إضافة تأخير للبحث
        let searchTimeout;
        searchInput.addEventListener('input', (e) => {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                performAdvancedSearch(e.target.value);
            }, 300);
        });

        // البحث عند الضغط على Enter
        searchInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                performAdvancedSearch(e.target.value);
            }
        });
    }
}

// تنفيذ البحث المتقدم
function performAdvancedSearch(query) {
    if (!query.trim()) {
        // إذا كان البحث فارغاً، أعرض كل المحادثات
        loadChats();
        return;
    }

    console.log('🔍 Searching for:', query);

    // البحث في المحادثات المحلية
    const chatItems = document.querySelectorAll('.chat-item');
    let visibleCount = 0;

    chatItems.forEach(item => {
        const chatName = item.querySelector('.chat-name')?.textContent.toLowerCase() || '';
        const previewText = item.querySelector('.preview-text')?.textContent.toLowerCase() || '';
        const searchTerm = query.toLowerCase();

        const matches = chatName.includes(searchTerm) || previewText.includes(searchTerm);

        if (matches) {
            item.style.display = 'flex';
            highlightSearchTerm(item, searchTerm);
            visibleCount++;
        } else {
            item.style.display = 'none';
        }
    });

    // إظهار رسالة إذا لم توجد نتائج
    if (visibleCount === 0) {
        showNoSearchResults(query);
    } else {
        hideNoSearchResults();
    }

    console.log(`🔍 Found ${visibleCount} results for "${query}"`);
}

// تمييز نص البحث
function highlightSearchTerm(item, searchTerm) {
    const chatName = item.querySelector('.chat-name');
    const previewText = item.querySelector('.preview-text');

    [chatName, previewText].forEach(element => {
        if (element) {
            const originalText = element.dataset.originalText || element.textContent;
            element.dataset.originalText = originalText;

            const regex = new RegExp(`(${searchTerm})`, 'gi');
            const highlightedText = originalText.replace(regex, '<mark>$1</mark>');
            element.innerHTML = highlightedText;
        }
    });
}

// إزالة تمييز البحث
function removeSearchHighlight() {
    const highlightedElements = document.querySelectorAll('[data-original-text]');
    highlightedElements.forEach(element => {
        element.textContent = element.dataset.originalText;
        delete element.dataset.originalText;
    });
}

// إظهار رسالة عدم وجود نتائج
function showNoSearchResults(query) {
    hideNoSearchResults();

    const noResultsDiv = document.createElement('div');
    noResultsDiv.id = 'noSearchResults';
    noResultsDiv.className = 'no-search-results';
    noResultsDiv.innerHTML = `
        <div class="no-results-icon">🔍</div>
        <h3>لا توجد نتائج</h3>
        <p>لم نجد أي محادثات تحتوي على "${query}"</p>
        <button onclick="clearSearch()" class="clear-search-btn">
            <i class="fas fa-times"></i> مسح البحث
        </button>
    `;

    const chatList = document.getElementById('chatList');
    chatList.appendChild(noResultsDiv);
}

// إخفاء رسالة عدم وجود نتائج
function hideNoSearchResults() {
    const noResultsDiv = document.getElementById('noSearchResults');
    if (noResultsDiv) {
        noResultsDiv.remove();
    }
}

// مسح البحث
function clearSearch() {
    const searchInput = document.getElementById('chatSearch');
    if (searchInput) {
        searchInput.value = '';
        performAdvancedSearch('');
        removeSearchHighlight();
    }
}

// البحث في الرسائل داخل المحادثة
async function searchInMessages(query) {
    if (!currentChatId || !query.trim()) return;

    try {
        const response = await fetch(`${API_BASE_URL}/messages/search?q=${encodeURIComponent(query)}&conversation_id=${currentChatId}`, {
            headers: {
                'Authorization': `Bearer ${authToken}`
            }
        });

        if (response.ok) {
            const results = await response.json();
            displaySearchResults(results, query);
        }
    } catch (error) {
        console.error('Error searching messages:', error);
    }
}

// عرض نتائج البحث في الرسائل
function displaySearchResults(results, query) {
    // TODO: تنفيذ عرض نتائج البحث في الرسائل
    console.log('Search results:', results);
    showNotification(`وجدت ${results.length} نتيجة لـ "${query}"`, 'info');
}

// 🎨 نظام الثيمات والتخصيص
const themes = {
    default: {
        name: 'الافتراضي',
        primary: '#667eea',
        secondary: '#764ba2',
        background: '#f8f9fa',
        surface: '#ffffff',
        text: '#333333'
    },
    dark: {
        name: 'الوضع الليلي',
        primary: '#4facfe',
        secondary: '#00f2fe',
        background: '#1a1a1a',
        surface: '#2d2d2d',
        text: '#ffffff'
    },
    ocean: {
        name: 'المحيط',
        primary: '#0093E9',
        secondary: '#80D0C7',
        background: '#f0f8ff',
        surface: '#ffffff',
        text: '#2c3e50'
    },
    sunset: {
        name: 'غروب الشمس',
        primary: '#ff6b6b',
        secondary: '#feca57',
        background: '#fff5f5',
        surface: '#ffffff',
        text: '#2d3436'
    },
    forest: {
        name: 'الغابة',
        primary: '#00b894',
        secondary: '#55a3ff',
        background: '#f8fff8',
        surface: '#ffffff',
        text: '#2d3436'
    }
};

let currentTheme = 'default';

function initializeThemeSystem() {
    // تحميل الثيم المحفوظ
    const savedTheme = localStorage.getItem('selectedTheme') || 'default';
    applyTheme(savedTheme);

    // إضافة مستمع لزر الإعدادات
    const settingsBtn = document.getElementById('settingsBtn');
    if (settingsBtn) {
        settingsBtn.addEventListener('click', showThemeSelector);
    }
}

function showThemeSelector() {
    const modal = document.createElement('div');
    modal.className = 'modal theme-modal';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h2><i class="fas fa-palette"></i> اختر الثيم</h2>
                <button class="modal-close" onclick="this.closest('.modal').remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="theme-grid">
                    ${Object.entries(themes).map(([key, theme]) => `
                        <div class="theme-option ${key === currentTheme ? 'active' : ''}"
                             onclick="selectTheme('${key}')">
                            <div class="theme-preview" style="
                                background: linear-gradient(135deg, ${theme.primary}, ${theme.secondary});
                            "></div>
                            <div class="theme-name">${theme.name}</div>
                            ${key === currentTheme ? '<i class="fas fa-check theme-check"></i>' : ''}
                        </div>
                    `).join('')}
                </div>
                <div class="theme-actions">
                    <button class="btn btn-secondary" onclick="resetToDefaultTheme()">
                        <i class="fas fa-undo"></i> الافتراضي
                    </button>
                    <button class="btn btn-primary" onclick="this.closest('.modal').remove()">
                        <i class="fas fa-check"></i> تم
                    </button>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    modal.style.display = 'flex';
}

function selectTheme(themeKey) {
    applyTheme(themeKey);

    // تحديث الواجهة
    document.querySelectorAll('.theme-option').forEach(option => {
        option.classList.remove('active');
        option.querySelector('.theme-check')?.remove();
    });

    const selectedOption = document.querySelector(`[onclick="selectTheme('${themeKey}')"]`);
    if (selectedOption) {
        selectedOption.classList.add('active');
        selectedOption.innerHTML += '<i class="fas fa-check theme-check"></i>';
    }
}

function applyTheme(themeKey) {
    const theme = themes[themeKey];
    if (!theme) return;

    currentTheme = themeKey;
    localStorage.setItem('selectedTheme', themeKey);

    // تطبيق متغيرات CSS
    const root = document.documentElement;
    root.style.setProperty('--theme-primary', theme.primary);
    root.style.setProperty('--theme-secondary', theme.secondary);
    root.style.setProperty('--theme-background', theme.background);
    root.style.setProperty('--theme-surface', theme.surface);
    root.style.setProperty('--theme-text', theme.text);

    // إضافة فئة الثيم للجسم
    document.body.className = document.body.className.replace(/theme-\w+/g, '');
    document.body.classList.add(`theme-${themeKey}`);

    console.log(`🎨 Applied theme: ${theme.name}`);
    showNotification(`تم تطبيق ثيم ${theme.name}`, 'success');
}

function resetToDefaultTheme() {
    selectTheme('default');
}

// تبديل الوضع الليلي بسرعة
function toggleDarkMode() {
    const isDark = currentTheme === 'dark';
    selectTheme(isDark ? 'default' : 'dark');
}

// ⌨️ نظام حالة الكتابة
let typingTimeout;
let isTyping = false;

function initializeTypingIndicator() {
    const messageInput = document.getElementById('messageInput');

    if (messageInput) {
        messageInput.addEventListener('input', handleTypingStart);
        messageInput.addEventListener('keydown', handleTypingStart);
        messageInput.addEventListener('blur', handleTypingStop);
    }
}

// بدء الكتابة
function handleTypingStart() {
    if (!currentChatId) return;

    // إرسال إشارة بدء الكتابة
    if (!isTyping) {
        isTyping = true;
        sendTypingStatus(true);
        console.log('⌨️ Started typing');
    }

    // إعادة تعيين مؤقت التوقف
    clearTimeout(typingTimeout);
    typingTimeout = setTimeout(() => {
        handleTypingStop();
    }, 3000); // توقف بعد 3 ثوان من عدم الكتابة
}

// توقف الكتابة
function handleTypingStop() {
    if (isTyping) {
        isTyping = false;
        sendTypingStatus(false);
        console.log('⌨️ Stopped typing');
    }

    clearTimeout(typingTimeout);
}

// إرسال حالة الكتابة
function sendTypingStatus(typing) {
    // TODO: إرسال حالة الكتابة للخادم عبر WebSocket
    console.log(`📡 Sending typing status: ${typing} to user ${currentChatId}`);

    // محاكاة استقبال حالة كتابة (للاختبار)
    if (typing && Math.random() > 0.7) {
        setTimeout(() => {
            showTypingIndicator();
            setTimeout(hideTypingIndicator, 2000);
        }, 1000);
    }
}

// إظهار مؤشر الكتابة
function showTypingIndicator() {
    const typingStatus = document.getElementById('typingStatus');
    if (typingStatus) {
        typingStatus.style.display = 'flex';
        console.log('👀 Showing typing indicator');
    }
}

// إخفاء مؤشر الكتابة
function hideTypingIndicator() {
    const typingStatus = document.getElementById('typingStatus');
    if (typingStatus) {
        typingStatus.style.display = 'none';
        console.log('👀 Hiding typing indicator');
    }
}

// استقبال حالة الكتابة من المستخدمين الآخرين
function onTypingStatusReceived(userId, typing) {
    if (userId === currentChatId) {
        if (typing) {
            showTypingIndicator();
        } else {
            hideTypingIndicator();
        }
    }
}

// تنظيف حالة الكتابة عند تغيير المحادثة
function clearTypingStatus() {
    handleTypingStop();
    hideTypingIndicator();
}
