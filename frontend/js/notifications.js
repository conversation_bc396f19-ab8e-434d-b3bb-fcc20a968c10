// نظام الإشعارات المتقدم
class NotificationSystem {
    constructor() {
        this.sounds = {
            default: '/static/sounds/notification.mp3',
            bell: '/static/sounds/bell.mp3',
            chime: '/static/sounds/chime.mp3',
            pop: '/static/sounds/pop.mp3',
            message: '/static/sounds/message.mp3',
            mention: '/static/sounds/mention.mp3'
        };
        
        this.settings = {
            enabled: true,
            sound: true,
            desktop: true,
            soundType: 'default',
            volume: 0.7,
            doNotDisturb: false,
            quietHours: {
                enabled: false,
                start: '22:00',
                end: '08:00'
            }
        };
        
        this.loadSettings();
        this.initializeAudio();
        this.requestPermissions();
    }

    // تحميل الإعدادات من localStorage
    loadSettings() {
        const saved = localStorage.getItem('notificationSettings');
        if (saved) {
            this.settings = { ...this.settings, ...JSON.parse(saved) };
        }
    }

    // حفظ الإعدادات
    saveSettings() {
        localStorage.setItem('notificationSettings', JSON.stringify(this.settings));
    }

    // تهيئة الصوت
    initializeAudio() {
        this.audioContext = null;
        this.audioBuffers = {};
        
        // تحميل ملفات الصوت مسبقاً
        Object.keys(this.sounds).forEach(soundType => {
            this.preloadSound(soundType);
        });
    }

    // تحميل ملف صوتي مسبقاً
    async preloadSound(soundType) {
        try {
            const audio = new Audio(this.sounds[soundType]);
            audio.preload = 'auto';
            audio.volume = this.settings.volume;
            this.audioBuffers[soundType] = audio;
        } catch (error) {
            console.warn(`Failed to preload sound: ${soundType}`, error);
        }
    }

    // طلب أذونات الإشعارات
    async requestPermissions() {
        if ('Notification' in window) {
            if (Notification.permission === 'default') {
                const permission = await Notification.requestPermission();
                this.settings.desktop = permission === 'granted';
                this.saveSettings();
            }
        }
    }

    // تشغيل صوت الإشعار
    playNotificationSound(soundType = null) {
        if (!this.settings.sound || this.isQuietTime()) return;

        const sound = soundType || this.settings.soundType;
        const audio = this.audioBuffers[sound];
        
        if (audio) {
            audio.volume = this.settings.volume;
            audio.currentTime = 0;
            audio.play().catch(error => {
                console.warn('Failed to play notification sound:', error);
            });
        }
    }

    // إظهار إشعار سطح المكتب
    showDesktopNotification(title, body, options = {}) {
        if (!this.settings.desktop || !this.settings.enabled || this.isQuietTime()) {
            return null;
        }

        if ('Notification' in window && Notification.permission === 'granted') {
            const notification = new Notification(title, {
                body: body,
                icon: options.icon || '/static/images/logo.png',
                tag: options.tag || 'chat-notification',
                badge: '/static/images/badge.png',
                requireInteraction: options.requireInteraction || false,
                silent: !this.settings.sound,
                timestamp: Date.now(),
                ...options
            });

            // إضافة مستمعي الأحداث
            notification.onclick = () => {
                window.focus();
                if (options.onClick) options.onClick();
                notification.close();
            };

            notification.onclose = () => {
                if (options.onClose) options.onClose();
            };

            // إغلاق تلقائي بعد 5 ثوان
            setTimeout(() => {
                notification.close();
            }, 5000);

            return notification;
        }

        return null;
    }

    // إشعار رسالة جديدة
    notifyNewMessage(message, sender) {
        const title = `رسالة جديدة من ${sender.username}`;
        const body = this.truncateMessage(message.content);
        
        this.showDesktopNotification(title, body, {
            tag: `message-${message.id}`,
            icon: sender.avatar_url || '/static/images/default-avatar.png',
            onClick: () => {
                // التركيز على المحادثة
                if (typeof openChat === 'function') {
                    openChat(sender.id);
                }
            }
        });

        this.playNotificationSound('message');
    }

    // إشعار ذكر في مجموعة
    notifyMention(message, sender, group) {
        const title = `تم ذكرك في ${group.name}`;
        const body = `${sender.username}: ${this.truncateMessage(message.content)}`;
        
        this.showDesktopNotification(title, body, {
            tag: `mention-${message.id}`,
            icon: group.avatar_url || '/static/images/group-icon.png',
            requireInteraction: true,
            onClick: () => {
                if (typeof openChat === 'function') {
                    openChat(group.id, 'group');
                }
            }
        });

        this.playNotificationSound('mention');
    }

    // إشعار مكالمة واردة
    notifyIncomingCall(caller, type = 'voice') {
        const title = `مكالمة ${type === 'video' ? 'مرئية' : 'صوتية'} واردة`;
        const body = `من ${caller.username}`;
        
        this.showDesktopNotification(title, body, {
            tag: `call-${caller.id}`,
            icon: caller.avatar_url || '/static/images/default-avatar.png',
            requireInteraction: true,
            actions: [
                { action: 'answer', title: 'رد' },
                { action: 'decline', title: 'رفض' }
            ]
        });

        this.playNotificationSound('bell');
    }

    // إشعار حالة المستخدم
    notifyUserStatus(user, status) {
        if (status === 'online') {
            const title = 'مستخدم متصل';
            const body = `${user.username} أصبح متصلاً`;
            
            this.showDesktopNotification(title, body, {
                tag: `status-${user.id}`,
                icon: user.avatar_url || '/static/images/default-avatar.png'
            });

            this.playNotificationSound('chime');
        }
    }

    // اقتطاع النص الطويل
    truncateMessage(text, maxLength = 100) {
        if (text.length <= maxLength) return text;
        return text.substring(0, maxLength) + '...';
    }

    // التحقق من وقت الهدوء
    isQuietTime() {
        if (!this.settings.quietHours.enabled) return false;

        const now = new Date();
        const currentTime = now.getHours() * 60 + now.getMinutes();
        
        const [startHour, startMin] = this.settings.quietHours.start.split(':').map(Number);
        const [endHour, endMin] = this.settings.quietHours.end.split(':').map(Number);
        
        const startTime = startHour * 60 + startMin;
        const endTime = endHour * 60 + endMin;

        if (startTime <= endTime) {
            return currentTime >= startTime && currentTime <= endTime;
        } else {
            // عبر منتصف الليل
            return currentTime >= startTime || currentTime <= endTime;
        }
    }

    // تفعيل/إلغاء وضع عدم الإزعاج
    setDoNotDisturb(enabled) {
        this.settings.doNotDisturb = enabled;
        this.saveSettings();
        
        if (enabled) {
            this.showInAppNotification('تم تفعيل وضع عدم الإزعاج', 'info');
        } else {
            this.showInAppNotification('تم إلغاء وضع عدم الإزعاج', 'info');
        }
    }

    // إشعار داخل التطبيق
    showInAppNotification(message, type = 'info', duration = 3000) {
        if (typeof showNotification === 'function') {
            showNotification(message, type);
        }
    }

    // تحديث إعدادات الصوت
    updateSoundSettings(settings) {
        this.settings = { ...this.settings, ...settings };
        this.saveSettings();
        
        // تحديث مستوى الصوت للملفات المحملة
        Object.values(this.audioBuffers).forEach(audio => {
            if (audio) audio.volume = this.settings.volume;
        });
    }

    // اختبار الإشعار
    testNotification() {
        this.showDesktopNotification(
            'اختبار الإشعارات',
            'هذا إشعار تجريبي للتأكد من عمل النظام',
            { tag: 'test-notification' }
        );
        
        this.playNotificationSound();
    }

    // تنظيف الموارد
    cleanup() {
        Object.values(this.audioBuffers).forEach(audio => {
            if (audio) {
                audio.pause();
                audio.src = '';
            }
        });
        
        this.audioBuffers = {};
    }
}

// إنشاء نظام الإشعارات العام
let notificationSystem = null;

// تهيئة نظام الإشعارات
function initializeNotifications() {
    if (!notificationSystem) {
        notificationSystem = new NotificationSystem();
    }
    return notificationSystem;
}

// دوال مساعدة للوصول السريع
function notifyNewMessage(message, sender) {
    if (notificationSystem) {
        notificationSystem.notifyNewMessage(message, sender);
    }
}

function notifyMention(message, sender, group) {
    if (notificationSystem) {
        notificationSystem.notifyMention(message, sender, group);
    }
}

function notifyIncomingCall(caller, type) {
    if (notificationSystem) {
        notificationSystem.notifyIncomingCall(caller, type);
    }
}

function playNotificationSound(soundType) {
    if (notificationSystem) {
        notificationSystem.playNotificationSound(soundType);
    }
}

// تصدير للاستخدام في ملفات أخرى
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { 
        NotificationSystem, 
        initializeNotifications,
        notifyNewMessage,
        notifyMention,
        notifyIncomingCall,
        playNotificationSound
    };
}
