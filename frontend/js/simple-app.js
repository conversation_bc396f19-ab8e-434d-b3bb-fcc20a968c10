// 🚀 تطبيق دردشة بسيط وفعال

// المتغيرات الأساسية
const API_BASE_URL = 'http://192.168.10.88:8000/api/v1';
let authToken = localStorage.getItem('authToken');
let currentUser = JSON.parse(localStorage.getItem('currentUser') || 'null');
let websocket = null;

// عناصر DOM
let loginPage, chatPage, chatList;

// تهيئة التطبيق
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 Starting simple chat app...');
    
    // تهيئة عناصر DOM
    loginPage = document.getElementById('loginPage');
    chatPage = document.getElementById('chatPage');
    chatList = document.getElementById('chatList');
    
    // إعداد الأحداث
    setupEventListeners();
    
    // التحقق من حالة المصادقة
    checkAuthStatus();
});

// إعداد مستمعي الأحداث
function setupEventListeners() {
    // نموذج تسجيل الدخول
    const loginForm = document.getElementById('loginForm');
    if (loginForm) {
        loginForm.addEventListener('submit', handleLogin);
    }
    
    // نموذج التسجيل
    const registerForm = document.getElementById('registerForm');
    if (registerForm) {
        registerForm.addEventListener('submit', handleRegister);
    }
    
    // زر محادثة جديدة
    const newChatBtn = document.getElementById('newChatBtn');
    if (newChatBtn) {
        newChatBtn.addEventListener('click', showNewChatModal);
    }
}

// التحقق من حالة المصادقة
function checkAuthStatus() {
    if (authToken && currentUser) {
        showChatPage();
        loadChats();
    } else {
        showLoginPage();
    }
}

// عرض صفحة تسجيل الدخول
function showLoginPage() {
    loginPage.classList.add('active');
    chatPage.classList.remove('active');
}

// عرض صفحة الدردشة
function showChatPage() {
    loginPage.classList.remove('active');
    chatPage.classList.add('active');
}

// معالجة تسجيل الدخول
async function handleLogin(event) {
    event.preventDefault();
    
    const formData = new FormData(event.target);
    const loginData = {
        username: formData.get('username'),
        password: formData.get('password')
    };
    
    console.log('🔐 Attempting login...');
    
    try {
        const response = await fetch(`${API_BASE_URL}/auth/login`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(loginData)
        });
        
        if (response.ok) {
            const data = await response.json();
            authToken = data.access_token;
            currentUser = data.user;
            
            // حفظ البيانات
            localStorage.setItem('authToken', authToken);
            localStorage.setItem('currentUser', JSON.stringify(currentUser));
            
            console.log('✅ Login successful');
            showNotification('تم تسجيل الدخول بنجاح!', 'success');
            showChatPage();
            loadChats();
            
        } else {
            console.error('❌ Login failed');
            showNotification('خطأ في اسم المستخدم أو كلمة المرور', 'error');
        }
    } catch (error) {
        console.error('❌ Login error:', error);
        showNotification('خطأ في الاتصال بالخادم', 'error');
    }
}

// تحميل المحادثات - بسيط وفعال
async function loadChats() {
    console.log('📱 Loading chats...');
    
    if (!authToken || !chatList) {
        console.log('❌ Missing requirements for loading chats');
        return;
    }
    
    try {
        const response = await fetch(`${API_BASE_URL}/messages/conversations`, {
            headers: {
                'Authorization': `Bearer ${authToken}`
            }
        });
        
        if (response.ok) {
            const conversations = await response.json();
            console.log('✅ Conversations loaded:', conversations);
            displayChatList(conversations);
        } else {
            console.error('❌ Failed to load chats:', response.status);
            showError('فشل في تحميل المحادثات');
        }
    } catch (error) {
        console.error('❌ Error loading chats:', error);
        showError('خطأ في الاتصال');
    }
}

// عرض قائمة المحادثات
function displayChatList(conversations) {
    console.log('🎨 Displaying chat list...');
    
    if (!chatList) {
        console.error('❌ chatList element not found');
        return;
    }
    
    // مسح المحتوى السابق
    chatList.innerHTML = '';
    
    if (!conversations || conversations.length === 0) {
        chatList.innerHTML = `
            <div class="no-chats-message">
                <i class="fas fa-comments"></i>
                <h3>لا توجد محادثات</h3>
                <p>ابدأ محادثة جديدة!</p>
            </div>
        `;
        return;
    }
    
    // إنشاء عناصر المحادثات
    conversations.forEach(conversation => {
        const chatItem = createChatItem(conversation);
        if (chatItem) {
            chatList.appendChild(chatItem);
        }
    });
    
    console.log(`✅ Displayed ${conversations.length} conversations`);
}

// إنشاء عنصر محادثة
function createChatItem(conversation) {
    if (!conversation || !conversation.id || !conversation.name) {
        console.error('❌ Invalid conversation data:', conversation);
        return null;
    }
    
    const chatItem = document.createElement('div');
    chatItem.className = 'chat-item';
    chatItem.dataset.chatId = conversation.id;
    
    // الصورة الرمزية
    const avatarContent = conversation.avatar_url ?
        `<img src="${conversation.avatar_url}" alt="${conversation.name}">` :
        `<div class="avatar-text">${conversation.name.charAt(0).toUpperCase()}</div>`;
    
    // آخر رسالة
    const lastMessage = conversation.last_message;
    const preview = lastMessage ? lastMessage.content : 'لا توجد رسائل';
    const timeAgo = lastMessage ? formatTime(lastMessage.created_at) : '';
    
    chatItem.innerHTML = `
        <div class="chat-avatar">
            ${avatarContent}
            ${conversation.is_online ? '<div class="online-indicator"></div>' : ''}
        </div>
        <div class="chat-content">
            <div class="chat-header">
                <h4 class="chat-name">${conversation.name}</h4>
                <span class="chat-time">${timeAgo}</span>
            </div>
            <div class="chat-preview">
                <span class="preview-text">${preview}</span>
                ${conversation.unread_count > 0 ? `<span class="unread-badge">${conversation.unread_count}</span>` : ''}
            </div>
        </div>
    `;
    
    // إضافة حدث النقر
    chatItem.addEventListener('click', () => {
        console.log('🖱️ Chat clicked:', conversation);
        openChat(conversation);
    });
    
    return chatItem;
}

// فتح محادثة
function openChat(conversation) {
    console.log('💬 Opening chat:', conversation);
    // TODO: تنفيذ فتح المحادثة
    showNotification(`فتح محادثة مع ${conversation.name}`, 'info');
}

// تنسيق الوقت
function formatTime(dateString) {
    try {
        const date = new Date(dateString);
        const now = new Date();
        const diff = now - date;
        
        if (diff < 60000) return 'الآن';
        if (diff < 3600000) return `${Math.floor(diff / 60000)} د`;
        if (diff < 86400000) return `${Math.floor(diff / 3600000)} س`;
        return `${Math.floor(diff / 86400000)} ي`;
    } catch (error) {
        return '';
    }
}

// عرض رسالة خطأ
function showError(message) {
    if (chatList) {
        chatList.innerHTML = `
            <div class="error-message">
                <i class="fas fa-exclamation-triangle"></i>
                <h3>خطأ</h3>
                <p>${message}</p>
                <button onclick="loadChats()" class="retry-btn">
                    إعادة المحاولة
                </button>
            </div>
        `;
    }
}

// عرض الإشعارات
function showNotification(message, type = 'info') {
    console.log(`📢 ${type.toUpperCase()}: ${message}`);
    
    // إنشاء عنصر الإشعار
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <span>${message}</span>
        <button onclick="this.parentElement.remove()">×</button>
    `;
    
    // إضافة الإشعار للصفحة
    document.body.appendChild(notification);
    
    // إزالة الإشعار بعد 3 ثوان
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 3000);
}

// إظهار نافذة محادثة جديدة
function showNewChatModal() {
    console.log('➕ Show new chat modal');
    showNotification('ميزة المحادثة الجديدة قريباً!', 'info');
}

// تصدير الدوال للاستخدام العام
window.loadChats = loadChats;
window.showNotification = showNotification;

console.log('✅ Simple chat app initialized');
