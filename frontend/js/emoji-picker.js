// مكون اختيار الرموز التعبيرية
class EmojiPicker {
    constructor() {
        this.isOpen = false;
        this.currentInput = null;
        this.emojis = this.getEmojiCategories();
        this.init();
    }

    init() {
        this.createEmojiPicker();
        this.setupEventListeners();
    }

    getEmojiCategories() {
        return {
            'الوجوه والمشاعر': [
                '😀', '😃', '😄', '😁', '😆', '😅', '🤣', '😂', '🙂', '🙃',
                '😉', '😊', '😇', '🥰', '😍', '🤩', '😘', '😗', '😚', '😙',
                '😋', '😛', '😜', '🤪', '😝', '🤑', '🤗', '🤭', '🤫', '🤔',
                '🤐', '🤨', '😐', '😑', '😶', '😏', '😒', '🙄', '😬', '🤥',
                '😔', '😪', '🤤', '😴', '😷', '🤒', '🤕', '🤢', '🤮', '🤧',
                '🥵', '🥶', '🥴', '😵', '🤯', '🤠', '🥳', '😎', '🤓', '🧐'
            ],
            'الأيدي والإيماءات': [
                '👋', '🤚', '🖐️', '✋', '🖖', '👌', '🤏', '✌️', '🤞', '🤟',
                '🤘', '🤙', '👈', '👉', '👆', '🖕', '👇', '☝️', '👍', '👎',
                '👊', '✊', '🤛', '🤜', '👏', '🙌', '👐', '🤲', '🤝', '🙏'
            ],
            'الحيوانات والطبيعة': [
                '🐶', '🐱', '🐭', '🐹', '🐰', '🦊', '🐻', '🐼', '🐨', '🐯',
                '🦁', '🐮', '🐷', '🐽', '🐸', '🐵', '🙈', '🙉', '🙊', '🐒',
                '🐔', '🐧', '🐦', '🐤', '🐣', '🐥', '🦆', '🦅', '🦉', '🦇',
                '🐺', '🐗', '🐴', '🦄', '🐝', '🐛', '🦋', '🐌', '🐞', '🐜'
            ],
            'الطعام والشراب': [
                '🍎', '🍊', '🍋', '🍌', '🍉', '🍇', '🍓', '🍈', '🍒', '🍑',
                '🥭', '🍍', '🥥', '🥝', '🍅', '🍆', '🥑', '🥦', '🥬', '🥒',
                '🌶️', '🌽', '🥕', '🧄', '🧅', '🥔', '🍠', '🥐', '🥖', '🍞',
                '🥨', '🧀', '🥚', '🍳', '🧈', '🥞', '🧇', '🥓', '🥩', '🍗'
            ],
            'الأنشطة والرياضة': [
                '⚽', '🏀', '🏈', '⚾', '🥎', '🎾', '🏐', '🏉', '🥏', '🎱',
                '🪀', '🏓', '🏸', '🏒', '🏑', '🥍', '🏏', '🪃', '🥅', '⛳',
                '🪁', '🏹', '🎣', '🤿', '🥊', '🥋', '🎽', '🛹', '🛷', '⛸️'
            ],
            'السفر والأماكن': [
                '🚗', '🚕', '🚙', '🚌', '🚎', '🏎️', '🚓', '🚑', '🚒', '🚐',
                '🛻', '🚚', '🚛', '🚜', '🏍️', '🛵', '🚲', '🛴', '🛺', '🚨',
                '🚔', '🚍', '🚘', '🚖', '🚡', '🚠', '🚟', '🚃', '🚋', '🚞'
            ],
            'الرموز والأشياء': [
                '❤️', '🧡', '💛', '💚', '💙', '💜', '🖤', '🤍', '🤎', '💔',
                '❣️', '💕', '💞', '💓', '💗', '💖', '💘', '💝', '💟', '☮️',
                '✝️', '☪️', '🕉️', '☸️', '✡️', '🔯', '🕎', '☯️', '☦️', '🛐'
            ]
        };
    }

    createEmojiPicker() {
        const picker = document.createElement('div');
        picker.id = 'emojiPicker';
        picker.className = 'emoji-picker';
        picker.style.display = 'none';

        let html = '<div class="emoji-picker-header">';
        html += '<div class="emoji-categories">';
        
        Object.keys(this.emojis).forEach((category, index) => {
            html += `<button class="emoji-category-btn ${index === 0 ? 'active' : ''}" data-category="${category}">${category}</button>`;
        });
        
        html += '</div>';
        html += '<button class="emoji-picker-close">×</button>';
        html += '</div>';
        
        html += '<div class="emoji-picker-content">';
        html += '<div class="emoji-search">';
        html += '<input type="text" placeholder="البحث عن رمز تعبيري..." class="emoji-search-input">';
        html += '</div>';
        html += '<div class="emoji-grid" id="emojiGrid">';
        
        // عرض الفئة الأولى افتراضياً
        const firstCategory = Object.keys(this.emojis)[0];
        this.emojis[firstCategory].forEach(emoji => {
            html += `<button class="emoji-btn" data-emoji="${emoji}">${emoji}</button>`;
        });
        
        html += '</div>';
        html += '</div>';

        picker.innerHTML = html;
        document.body.appendChild(picker);
    }

    setupEventListeners() {
        const picker = document.getElementById('emojiPicker');
        
        // إغلاق منتقي الرموز التعبيرية
        picker.querySelector('.emoji-picker-close').addEventListener('click', () => {
            this.hide();
        });

        // تبديل الفئات
        picker.querySelectorAll('.emoji-category-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const category = e.target.dataset.category;
                this.showCategory(category);
                
                // تحديث الفئة النشطة
                picker.querySelectorAll('.emoji-category-btn').forEach(b => b.classList.remove('active'));
                e.target.classList.add('active');
            });
        });

        // البحث في الرموز التعبيرية
        picker.querySelector('.emoji-search-input').addEventListener('input', (e) => {
            this.searchEmojis(e.target.value);
        });

        // اختيار رمز تعبيري
        picker.addEventListener('click', (e) => {
            if (e.target.classList.contains('emoji-btn')) {
                const emoji = e.target.dataset.emoji;
                this.insertEmoji(emoji);
            }
        });

        // إغلاق عند النقر خارج المنتقي
        document.addEventListener('click', (e) => {
            if (this.isOpen && !picker.contains(e.target) && !e.target.classList.contains('emoji-trigger')) {
                this.hide();
            }
        });
    }

    show(inputElement, triggerElement) {
        this.currentInput = inputElement;
        this.isOpen = true;
        
        const picker = document.getElementById('emojiPicker');
        picker.style.display = 'block';
        
        // تحديد موقع المنتقي
        const rect = triggerElement.getBoundingClientRect();
        picker.style.position = 'fixed';
        picker.style.top = (rect.top - picker.offsetHeight - 10) + 'px';
        picker.style.left = rect.left + 'px';
        
        // التأكد من أن المنتقي داخل الشاشة
        const pickerRect = picker.getBoundingClientRect();
        if (pickerRect.right > window.innerWidth) {
            picker.style.left = (window.innerWidth - pickerRect.width - 10) + 'px';
        }
        if (pickerRect.top < 0) {
            picker.style.top = (rect.bottom + 10) + 'px';
        }
    }

    hide() {
        this.isOpen = false;
        document.getElementById('emojiPicker').style.display = 'none';
        this.currentInput = null;
    }

    showCategory(category) {
        const grid = document.getElementById('emojiGrid');
        let html = '';
        
        this.emojis[category].forEach(emoji => {
            html += `<button class="emoji-btn" data-emoji="${emoji}">${emoji}</button>`;
        });
        
        grid.innerHTML = html;
    }

    searchEmojis(query) {
        if (!query.trim()) {
            // إذا كان البحث فارغاً، عرض الفئة النشطة
            const activeCategory = document.querySelector('.emoji-category-btn.active').dataset.category;
            this.showCategory(activeCategory);
            return;
        }

        const grid = document.getElementById('emojiGrid');
        let html = '';
        
        // البحث في جميع الرموز التعبيرية
        Object.values(this.emojis).flat().forEach(emoji => {
            // يمكن تحسين البحث هنا بإضافة أسماء الرموز التعبيرية
            html += `<button class="emoji-btn" data-emoji="${emoji}">${emoji}</button>`;
        });
        
        grid.innerHTML = html;
    }

    insertEmoji(emoji) {
        if (this.currentInput) {
            const start = this.currentInput.selectionStart;
            const end = this.currentInput.selectionEnd;
            const text = this.currentInput.value;
            
            this.currentInput.value = text.substring(0, start) + emoji + text.substring(end);
            this.currentInput.selectionStart = this.currentInput.selectionEnd = start + emoji.length;
            this.currentInput.focus();
            
            // إطلاق حدث input للتحديث
            this.currentInput.dispatchEvent(new Event('input', { bubbles: true }));
        }
        
        this.hide();
    }
}

// إنشاء منتقي الرموز التعبيرية العام
let emojiPicker = null;

// دالة لإظهار منتقي الرموز التعبيرية
function showEmojiPicker(inputElement, triggerElement) {
    if (!emojiPicker) {
        emojiPicker = new EmojiPicker();
    }
    emojiPicker.show(inputElement, triggerElement);
}

// تصدير للاستخدام في ملفات أخرى
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { EmojiPicker, showEmojiPicker };
}
