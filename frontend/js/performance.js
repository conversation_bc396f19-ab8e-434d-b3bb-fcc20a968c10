// تحسينات الأداء والتجربة
class PerformanceOptimizer {
    constructor() {
        this.messageCache = new Map();
        this.userCache = new Map();
        this.imageCache = new Map();
        this.lazyLoadObserver = null;
        this.virtualScrolling = false;
        this.debounceTimers = new Map();
        
        this.init();
    }

    init() {
        this.setupLazyLoading();
        this.setupVirtualScrolling();
        this.setupImageOptimization();
        this.setupConnectionOptimization();
        this.setupMemoryManagement();
    }

    // إعداد التحميل التدريجي للصور
    setupLazyLoading() {
        if ('IntersectionObserver' in window) {
            this.lazyLoadObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        this.loadImage(img);
                        this.lazyLoadObserver.unobserve(img);
                    }
                });
            }, {
                rootMargin: '50px 0px',
                threshold: 0.01
            });
        }
    }

    // تحميل الصورة مع التحسين
    async loadImage(img) {
        const src = img.dataset.src;
        if (!src) return;

        try {
            // التحقق من الكاش أولاً
            if (this.imageCache.has(src)) {
                img.src = this.imageCache.get(src);
                img.classList.add('loaded');
                return;
            }

            // إنشاء صورة مؤقتة للتحميل
            const tempImg = new Image();
            tempImg.onload = () => {
                // إضافة للكاش
                this.imageCache.set(src, src);
                
                // تطبيق الصورة مع تأثير انتقالي
                img.src = src;
                img.classList.add('loaded');
                
                // تحسين الذاكرة - حذف الصور القديمة من الكاش
                if (this.imageCache.size > 100) {
                    const firstKey = this.imageCache.keys().next().value;
                    this.imageCache.delete(firstKey);
                }
            };
            
            tempImg.onerror = () => {
                img.classList.add('error');
                img.alt = 'فشل في تحميل الصورة';
            };
            
            tempImg.src = src;
            
        } catch (error) {
            console.error('Error loading image:', error);
            img.classList.add('error');
        }
    }

    // إعداد التمرير الافتراضي للرسائل
    setupVirtualScrolling() {
        const messagesContainer = document.getElementById('messagesContainer');
        if (!messagesContainer) return;

        let isScrolling = false;
        let scrollTimeout;

        messagesContainer.addEventListener('scroll', () => {
            if (!isScrolling) {
                // بداية التمرير
                messagesContainer.classList.add('scrolling');
                isScrolling = true;
            }

            // إعادة تعيين المؤقت
            clearTimeout(scrollTimeout);
            scrollTimeout = setTimeout(() => {
                // انتهاء التمرير
                messagesContainer.classList.remove('scrolling');
                isScrolling = false;
                
                // تحميل الرسائل الإضافية إذا لزم الأمر
                this.checkLoadMoreMessages();
            }, 150);
        });
    }

    // التحقق من الحاجة لتحميل رسائل إضافية
    checkLoadMoreMessages() {
        const messagesContainer = document.getElementById('messagesContainer');
        if (!messagesContainer) return;

        const scrollTop = messagesContainer.scrollTop;
        const scrollHeight = messagesContainer.scrollHeight;
        const clientHeight = messagesContainer.clientHeight;

        // إذا وصل المستخدم لأعلى الحاوية، حمّل رسائل أقدم
        if (scrollTop < 100) {
            this.loadOlderMessages();
        }
    }

    // تحميل رسائل أقدم
    async loadOlderMessages() {
        if (!currentChatId || this.isLoadingMessages) return;
        
        this.isLoadingMessages = true;
        
        try {
            const oldestMessage = document.querySelector('.message');
            const oldestMessageId = oldestMessage ? oldestMessage.dataset.messageId : null;
            
            // طلب الرسائل الأقدم من الخادم
            const response = await fetch(`/api/v1/messages/conversation/${currentChatId}?before=${oldestMessageId}&limit=20`, {
                headers: {
                    'Authorization': `Bearer ${authToken}`
                }
            });

            if (response.ok) {
                const messages = await response.json();
                
                if (messages.length > 0) {
                    // إضافة الرسائل في أعلى الحاوية
                    this.prependMessages(messages);
                }
            }
        } catch (error) {
            console.error('Error loading older messages:', error);
        } finally {
            this.isLoadingMessages = false;
        }
    }

    // إضافة رسائل في أعلى الحاوية
    prependMessages(messages) {
        const messagesContainer = document.getElementById('messagesContainer');
        const fragment = document.createDocumentFragment();
        
        messages.reverse().forEach(message => {
            const messageElement = createMessageElement(message);
            fragment.appendChild(messageElement);
        });
        
        // حفظ موقع التمرير الحالي
        const scrollHeight = messagesContainer.scrollHeight;
        
        // إضافة الرسائل
        messagesContainer.insertBefore(fragment, messagesContainer.firstChild);
        
        // استعادة موقع التمرير
        messagesContainer.scrollTop = messagesContainer.scrollHeight - scrollHeight;
    }

    // تحسين الصور
    setupImageOptimization() {
        // ضغط الصور قبل الرفع
        this.setupImageCompression();
        
        // تحسين عرض الصور
        this.setupImageDisplay();
    }

    // ضغط الصور
    setupImageCompression() {
        const fileInput = document.getElementById('avatarInput');
        if (fileInput) {
            fileInput.addEventListener('change', (e) => {
                const file = e.target.files[0];
                if (file && file.type.startsWith('image/')) {
                    this.compressImage(file).then(compressedFile => {
                        // استبدال الملف الأصلي بالملف المضغوط
                        const dataTransfer = new DataTransfer();
                        dataTransfer.items.add(compressedFile);
                        fileInput.files = dataTransfer.files;
                    });
                }
            });
        }
    }

    // ضغط صورة
    async compressImage(file, maxWidth = 800, quality = 0.8) {
        return new Promise((resolve) => {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            const img = new Image();
            
            img.onload = () => {
                // حساب الأبعاد الجديدة
                let { width, height } = img;
                
                if (width > maxWidth) {
                    height = (height * maxWidth) / width;
                    width = maxWidth;
                }
                
                // تعيين أبعاد الكانفاس
                canvas.width = width;
                canvas.height = height;
                
                // رسم الصورة المضغوطة
                ctx.drawImage(img, 0, 0, width, height);
                
                // تحويل إلى blob
                canvas.toBlob(resolve, file.type, quality);
            };
            
            img.src = URL.createObjectURL(file);
        });
    }

    // تحسين عرض الصور
    setupImageDisplay() {
        document.addEventListener('click', (e) => {
            if (e.target.matches('.image-message img')) {
                this.openImageViewer(e.target.src, e.target.alt);
            }
        });
    }

    // عارض الصور المحسن
    openImageViewer(src, alt) {
        const viewer = document.createElement('div');
        viewer.className = 'image-viewer';
        viewer.innerHTML = `
            <div class="image-viewer-backdrop"></div>
            <div class="image-viewer-content">
                <img src="${src}" alt="${alt}" class="image-viewer-img">
                <button class="image-viewer-close">
                    <i class="fas fa-times"></i>
                </button>
                <div class="image-viewer-controls">
                    <button class="image-viewer-download" onclick="downloadImage('${src}', '${alt}')">
                        <i class="fas fa-download"></i>
                    </button>
                    <button class="image-viewer-zoom-in">
                        <i class="fas fa-search-plus"></i>
                    </button>
                    <button class="image-viewer-zoom-out">
                        <i class="fas fa-search-minus"></i>
                    </button>
                </div>
            </div>
        `;
        
        document.body.appendChild(viewer);
        
        // إضافة مستمعي الأحداث
        viewer.querySelector('.image-viewer-close').addEventListener('click', () => {
            document.body.removeChild(viewer);
        });
        
        viewer.querySelector('.image-viewer-backdrop').addEventListener('click', () => {
            document.body.removeChild(viewer);
        });
        
        // تأثير الظهور
        setTimeout(() => viewer.classList.add('active'), 10);
    }

    // تحسين الاتصال
    setupConnectionOptimization() {
        // تجميع الطلبات
        this.requestQueue = [];
        this.isProcessingQueue = false;
        
        // معالجة الطلبات كل 100ms
        setInterval(() => {
            this.processRequestQueue();
        }, 100);
    }

    // معالجة قائمة انتظار الطلبات
    async processRequestQueue() {
        if (this.isProcessingQueue || this.requestQueue.length === 0) return;
        
        this.isProcessingQueue = true;
        
        try {
            const requests = this.requestQueue.splice(0, 5); // معالجة 5 طلبات كحد أقصى
            
            await Promise.all(requests.map(request => request()));
        } catch (error) {
            console.error('Error processing request queue:', error);
        } finally {
            this.isProcessingQueue = false;
        }
    }

    // إضافة طلب لقائمة الانتظار
    queueRequest(requestFunction) {
        this.requestQueue.push(requestFunction);
    }

    // إدارة الذاكرة
    setupMemoryManagement() {
        // تنظيف الكاش كل 5 دقائق
        setInterval(() => {
            this.cleanupCache();
        }, 5 * 60 * 1000);
        
        // تنظيف عند إخفاء الصفحة
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.cleanupCache();
            }
        });
    }

    // تنظيف الكاش
    cleanupCache() {
        // تنظيف كاش الرسائل القديمة
        if (this.messageCache.size > 500) {
            const entries = Array.from(this.messageCache.entries());
            const toKeep = entries.slice(-250); // الاحتفاظ بآخر 250 رسالة
            
            this.messageCache.clear();
            toKeep.forEach(([key, value]) => {
                this.messageCache.set(key, value);
            });
        }
        
        // تنظيف كاش الصور
        if (this.imageCache.size > 50) {
            const entries = Array.from(this.imageCache.entries());
            const toKeep = entries.slice(-25); // الاحتفاظ بآخر 25 صورة
            
            this.imageCache.clear();
            toKeep.forEach(([key, value]) => {
                this.imageCache.set(key, value);
            });
        }
    }

    // دالة debounce للتحسين
    debounce(func, delay, key) {
        if (this.debounceTimers.has(key)) {
            clearTimeout(this.debounceTimers.get(key));
        }
        
        const timer = setTimeout(() => {
            func();
            this.debounceTimers.delete(key);
        }, delay);
        
        this.debounceTimers.set(key, timer);
    }

    // تحسين البحث
    optimizedSearch(query, callback) {
        this.debounce(() => {
            callback(query);
        }, 300, 'search');
    }
}

// إنشاء محسن الأداء العام
let performanceOptimizer = null;

// تهيئة محسن الأداء
function initializePerformanceOptimizer() {
    if (!performanceOptimizer) {
        performanceOptimizer = new PerformanceOptimizer();
    }
    return performanceOptimizer;
}

// دالة تحميل الصورة مع التحسين
function loadOptimizedImage(img) {
    if (performanceOptimizer) {
        performanceOptimizer.loadImage(img);
    }
}

// دالة تحميل الصورة
function downloadImage(src, filename) {
    const link = document.createElement('a');
    link.href = src;
    link.download = filename || 'image.jpg';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// تصدير للاستخدام في ملفات أخرى
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { 
        PerformanceOptimizer, 
        initializePerformanceOptimizer,
        loadOptimizedImage,
        downloadImage
    };
}
