<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - Time Chat</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 0;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .login-box {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            width: 100%;
            max-width: 400px;
            text-align: center;
        }
        
        h1 {
            color: #333;
            margin-bottom: 30px;
        }
        
        .form-group {
            margin-bottom: 20px;
            text-align: right;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            color: #555;
            font-weight: bold;
        }
        
        input {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            box-sizing: border-box;
        }
        
        input:focus {
            border-color: #667eea;
            outline: none;
        }
        
        button {
            width: 100%;
            padding: 15px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 18px;
            cursor: pointer;
            margin-top: 20px;
        }
        
        button:hover {
            background: #5a6fd8;
        }
        
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .message {
            margin-top: 15px;
            padding: 10px;
            border-radius: 5px;
            text-align: center;
        }
        
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="login-box">
        <h1>💬 Time Chat</h1>
        <p>تسجيل الدخول</p>
        
        <div class="form-group">
            <label>اسم المستخدم:</label>
            <input type="text" id="username" value="user123" placeholder="أدخل اسم المستخدم">
        </div>
        
        <div class="form-group">
            <label>كلمة المرور:</label>
            <input type="password" id="password" value="user123" placeholder="أدخل كلمة المرور">
        </div>
        
        <button id="loginBtn" onclick="login()">دخول</button>
        
        <div id="message" class="message" style="display: none;"></div>
    </div>

    <script>
        async function login() {
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value.trim();
            const messageDiv = document.getElementById('message');
            const loginBtn = document.getElementById('loginBtn');
            
            console.log('🔐 Login attempt:', username);
            
            if (!username || !password) {
                showMessage('يرجى ملء جميع الحقول', 'error');
                return;
            }
            
            // تعطيل الزر
            loginBtn.disabled = true;
            loginBtn.innerHTML = 'جاري تسجيل الدخول...';
            showMessage('جاري تسجيل الدخول...', 'info');
            
            try {
                const response = await fetch('http://*************:8000/api/v1/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: username,
                        password: password
                    })
                });
                
                console.log('📡 Response status:', response.status);
                
                if (response.ok) {
                    const data = await response.json();
                    console.log('✅ Login successful:', data);
                    
                    showMessage('تم تسجيل الدخول بنجاح! جاري التحويل...', 'success');
                    
                    // حفظ التوكن
                    localStorage.setItem('authToken', data.access_token);
                    
                    // التحويل للصفحة الرئيسية
                    setTimeout(() => {
                        window.location.href = '/';
                    }, 2000);
                    
                } else {
                    const errorData = await response.json().catch(() => ({}));
                    console.error('❌ Login failed:', errorData);
                    showMessage('اسم المستخدم أو كلمة المرور غير صحيحة', 'error');
                }
                
            } catch (error) {
                console.error('💥 Network error:', error);
                showMessage('حدث خطأ في الاتصال بالخادم', 'error');
            } finally {
                loginBtn.disabled = false;
                loginBtn.innerHTML = 'دخول';
            }
        }
        
        function showMessage(text, type) {
            const messageDiv = document.getElementById('message');
            messageDiv.innerHTML = text;
            messageDiv.className = 'message ' + type;
            messageDiv.style.display = 'block';
        }
        
        // مستمع Enter
        document.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                login();
            }
        });
        
        console.log('🚀 Simple login page loaded');
    </script>
</body>
</html>
