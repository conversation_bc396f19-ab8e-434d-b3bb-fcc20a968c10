<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تطبيق الدردشة الفورية</title>
    <link rel="stylesheet" href="/static/css/style.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- صفحة تسجيل الدخول -->
    <div id="loginPage" class="page login-page active">
        <div class="login-container">
            <div class="login-card">
                <div class="login-header">
                    <i class="fas fa-comments"></i>
                    <h1>تطبيق الدردشة الفورية</h1>
                    <p>تواصل مع أصدقائك في الوقت الفعلي</p>
                </div>
                
                <div class="login-tabs">
                    <button class="tab-btn active" data-tab="login">تسجيل الدخول</button>
                    <button class="tab-btn" data-tab="register">إنشاء حساب</button>
                </div>
                
                <!-- نموذج تسجيل الدخول البسيط -->
                <div id="loginForm" class="auth-form active">
                    <h3>تسجيل الدخول</h3>

                    <input type="text" id="simpleUsername" placeholder="اسم المستخدم" value="user123" style="width: 100%; padding: 10px; margin: 10px 0; border: 1px solid #ddd; border-radius: 5px;">

                    <input type="password" id="simplePassword" placeholder="كلمة المرور" value="user123" style="width: 100%; padding: 10px; margin: 10px 0; border: 1px solid #ddd; border-radius: 5px;">

                    <button id="simpleLoginBtn" style="width: 100%; padding: 12px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; font-size: 16px;">
                        دخول
                    </button>

                    <div id="loginMessage" style="margin-top: 10px; padding: 10px; text-align: center;"></div>

                    <div style="margin-top: 20px; text-align: center;">
                        <p>أو</p>
                        <a href="/simple-login.html" style="color: #007bff; text-decoration: none;">
                            استخدم صفحة تسجيل الدخول البسيطة
                        </a>
                    </div>
                </div>
                
                <!-- نموذج التسجيل -->
                <div id="registerForm" class="auth-form">
                    <div class="form-group">
                        <label for="registerUsername">اسم المستخدم</label>
                        <div class="input-group">
                            <i class="fas fa-user"></i>
                            <input type="text" id="registerUsername" name="username" required placeholder="أدخل اسم المستخدم">
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="registerEmail">البريد الإلكتروني</label>
                        <div class="input-group">
                            <i class="fas fa-envelope"></i>
                            <input type="email" id="registerEmail" name="email" required placeholder="أدخل بريدك الإلكتروني">
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="registerPassword">كلمة المرور</label>
                        <div class="input-group">
                            <i class="fas fa-lock"></i>
                            <input type="password" id="registerPassword" name="password" required placeholder="أدخل كلمة المرور">
                            <button type="button" class="toggle-password">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="confirmPassword">تأكيد كلمة المرور</label>
                        <div class="input-group">
                            <i class="fas fa-lock"></i>
                            <input type="password" id="confirmPassword" name="confirmPassword" required placeholder="أعد إدخال كلمة المرور">
                        </div>
                    </div>
                    
                    <button type="button" id="registerBtn" class="auth-btn">
                        <i class="fas fa-user-plus"></i>
                        إنشاء حساب
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- صفحة الدردشة الجميلة -->
    <div id="chatPage" class="page chat-page">
        <div class="chat-container">
            <!-- الشريط العلوي -->
            <header class="chat-header">
                <div class="header-left">
                    <div class="user-info">
                        <div class="user-avatar">
                            <i class="fas fa-user"></i>
                        </div>
                        <div class="user-details">
                            <h3 id="currentUserName">المستخدم</h3>
                            <span class="user-status online">متصل</span>
                        </div>
                    </div>
                </div>
                
                <div class="header-right">
                    <button class="header-btn" id="profileBtn" title="البروفايل">
                        <i class="fas fa-user"></i>
                    </button>
                    <button class="header-btn" id="settingsBtn" title="الإعدادات">
                        <i class="fas fa-cog"></i>
                    </button>
                    <button class="header-btn" id="logoutBtn" title="تسجيل الخروج">
                        <i class="fas fa-sign-out-alt"></i>
                    </button>
                </div>
            </header>
            
            <!-- المحتوى الرئيسي -->
            <div class="chat-main">
                <!-- الشريط الجانبي -->
                <aside class="chat-sidebar">
                    <div class="sidebar-header">
                        <h3>المحادثات</h3>
                        <div class="header-actions">
                            <button class="new-chat-btn" id="refreshChatsBtn" title="تحديث المحادثات" onclick="loadChatsWithValidation()" style="margin-left: 5px;">
                                <i class="fas fa-sync-alt"></i>
                            </button>
                            <button class="new-chat-btn" id="newChatBtn" title="محادثة جديدة">
                                <i class="fas fa-plus"></i>
                            </button>
                            <button class="new-group-btn" id="newGroupBtn" title="مجموعة جديدة">
                                <i class="fas fa-users"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="chat-search">
                        <div class="search-input">
                            <i class="fas fa-search"></i>
                            <input type="text" placeholder="البحث في المحادثات...">
                        </div>
                    </div>
                    
                    <div class="chat-list scrollbar-thin" id="chatList">
                        <!-- قائمة المحادثات ستظهر هنا -->
                    </div>
                </aside>
                
                <!-- منطقة الدردشة -->
                <main class="chat-content">
                    <div class="welcome-screen" id="welcomeScreen">
                        <div class="welcome-content">
                            <i class="fas fa-comments"></i>
                            <h2>مرحباً بك في تطبيق الدردشة</h2>
                            <p>اختر محادثة من القائمة أو ابدأ محادثة جديدة</p>
                        </div>
                    </div>
                    
                    <div class="chat-window" id="chatWindow" style="display: none;">
                        <!-- رأس المحادثة -->
                        <div class="chat-window-header">
                            <div class="chat-contact">
                                <div class="contact-avatar">
                                    <i class="fas fa-user"></i>
                                </div>
                                <div class="contact-info">
                                    <h4 id="chatContactName">اسم المستخدم</h4>
                                    <span class="contact-status" id="chatContactStatus">متصل</span>
                                    <div class="typing-status" id="typingStatus" style="display: none;">
                                        <span class="typing-text">يكتب</span>
                                        <div class="typing-dots">
                                            <span></span>
                                            <span></span>
                                            <span></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="chat-actions">
                                <button class="action-btn" id="searchBtn" title="البحث في المحادثة">
                                    <i class="fas fa-search"></i>
                                </button>
                                <button class="action-btn" id="callBtn" title="مكالمة صوتية">
                                    <i class="fas fa-phone"></i>
                                </button>
                                <button class="action-btn" id="videoBtn" title="مكالمة مرئية">
                                    <i class="fas fa-video"></i>
                                </button>
                                <button class="action-btn" id="profileBtn" title="الملف الشخصي">
                                    <i class="fas fa-user"></i>
                                </button>
                                <button class="action-btn" id="moreBtn" title="المزيد">
                                    <i class="fas fa-ellipsis-v"></i>
                                </button>
                            </div>
                        </div>

                        <!-- شريط البحث -->
                        <div class="search-bar" id="searchBar" style="display: none;">
                            <div class="search-input-container">
                                <input type="text" id="messageSearchInput" placeholder="البحث في الرسائل...">
                                <button class="search-close-btn" id="closeSearchBtn">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                            <div class="search-results" id="searchResults">
                                <!-- نتائج البحث ستظهر هنا -->
                            </div>
                        </div>

                        <!-- منطقة الرسائل -->
                        <div class="messages-container" id="messagesContainer">
                            <!-- الرسائل ستظهر هنا -->
                        </div>
                        
                        <!-- مؤشر الكتابة -->
                        <div class="typing-indicator" id="typingIndicator" style="display: none;">
                            <div class="typing-dots">
                                <span></span>
                                <span></span>
                                <span></span>
                            </div>
                            <span class="typing-text">يكتب...</span>
                        </div>
                        
                        <!-- منطقة إرسال الرسائل -->
                        <div class="message-input-container">
                            <form id="messageForm" class="message-form">
                                <div class="input-actions">
                                    <button type="button" class="input-btn emoji-trigger" id="emojiBtn" title="رموز تعبيرية">
                                        <i class="fas fa-smile"></i>
                                    </button>
                                    <button type="button" class="input-btn" id="fileBtn" title="إرفاق ملف">
                                        <i class="fas fa-paperclip"></i>
                                    </button>
                                    <button type="button" class="input-btn" id="voiceBtn" title="رسالة صوتية">
                                        <i class="fas fa-microphone"></i>
                                    </button>
                                </div>
                                
                                <div class="message-input">
                                    <input type="text" id="messageInput" placeholder="اكتب رسالتك هنا..." autocomplete="off">
                                </div>
                                
                                <button type="submit" class="send-btn" id="sendBtn">
                                    <i class="fas fa-paper-plane"></i>
                                </button>
                            </form>
                        </div>
                    </div>
                </main>
            </div>
        </div>
    </div>
    
    <!-- نافذة منبثقة للمحادثة الجديدة -->
    <div id="newChatModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>بدء محادثة جديدة</h3>
                <button class="close-btn" id="closeModalBtn">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <div class="modal-body">
                <div class="search-users">
                    <input type="text" id="userSearchInput" placeholder="البحث عن مستخدم...">
                </div>
                
                <div class="users-list" id="usersList">
                    <!-- قائمة المستخدمين ستظهر هنا -->
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة إنشاء مجموعة جديدة -->
    <div id="newGroupModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>إنشاء مجموعة جديدة</h3>
                <button class="close-btn" id="closeGroupModalBtn">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="createGroupForm">
                    <div class="form-group">
                        <label for="groupName">اسم المجموعة</label>
                        <input type="text" id="groupName" name="groupName" required placeholder="أدخل اسم المجموعة">
                    </div>
                    <div class="form-group">
                        <label for="groupDescription">وصف المجموعة (اختياري)</label>
                        <textarea id="groupDescription" name="groupDescription" placeholder="أدخل وصف المجموعة"></textarea>
                    </div>
                    <div class="form-group">
                        <label for="groupType">نوع المجموعة</label>
                        <select id="groupType" name="groupType">
                            <option value="private">مجموعة خاصة</option>
                            <option value="public">مجموعة عامة</option>
                            <option value="channel">قناة بث</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>إضافة أعضاء</label>
                        <div class="search-users">
                            <input type="text" id="memberSearchInput" placeholder="البحث عن مستخدمين لإضافتهم...">
                        </div>
                        <div class="selected-members" id="selectedMembers">
                            <!-- الأعضاء المختارون سيظهرون هنا -->
                        </div>
                        <div class="members-list" id="membersList">
                            <!-- قائمة المستخدمين للاختيار ستظهر هنا -->
                        </div>
                    </div>
                    <div class="form-actions">
                        <button type="button" class="btn btn-secondary" id="cancelGroupBtn">إلغاء</button>
                        <button type="submit" class="btn btn-primary">إنشاء المجموعة</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- نافذة الملف الشخصي -->
    <div id="profileModal" class="modal">
        <div class="modal-content profile-modal">
            <div class="modal-header">
                <h3>الملف الشخصي</h3>
                <button class="close-btn" id="closeProfileModalBtn">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="profile-section">
                    <div class="profile-avatar-section">
                        <div class="profile-avatar" id="profileAvatar">
                            <span id="profileInitial">M</span>
                        </div>
                        <button class="change-avatar-btn" id="changeAvatarBtn">
                            <i class="fas fa-camera"></i>
                            تغيير الصورة
                        </button>
                        <input type="file" id="avatarInput" accept="image/*" style="display: none;">
                    </div>

                    <form id="profileForm" class="profile-form">
                        <div class="form-group">
                            <label for="profileUsername">اسم المستخدم</label>
                            <input type="text" id="profileUsername" name="username" readonly>
                        </div>

                        <div class="form-group">
                            <label for="profileEmail">البريد الإلكتروني</label>
                            <input type="email" id="profileEmail" name="email">
                        </div>

                        <div class="form-group">
                            <label for="profileDisplayName">الاسم المعروض</label>
                            <input type="text" id="profileDisplayName" name="displayName" placeholder="اسمك الكامل">
                        </div>

                        <div class="form-group">
                            <label for="profileBio">نبذة شخصية</label>
                            <textarea id="profileBio" name="bio" placeholder="اكتب نبذة عن نفسك..."></textarea>
                        </div>

                        <div class="form-group">
                            <label for="profileStatus">الحالة</label>
                            <select id="profileStatus" name="status">
                                <option value="online">متصل</option>
                                <option value="away">بعيد</option>
                                <option value="busy">مشغول</option>
                                <option value="invisible">غير مرئي</option>
                            </select>
                        </div>

                        <div class="form-actions">
                            <button type="button" class="btn btn-secondary" id="cancelProfileBtn">إلغاء</button>
                            <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                        </div>
                    </form>
                </div>

                <div class="profile-tabs">
                    <div class="tab-buttons">
                        <button class="tab-btn active" data-tab="settings">الإعدادات</button>
                        <button class="tab-btn" data-tab="privacy">الخصوصية</button>
                        <button class="tab-btn" data-tab="notifications">الإشعارات</button>
                    </div>

                    <div class="tab-content">
                        <!-- تبويب الإعدادات -->
                        <div class="tab-panel active" id="settings-tab">
                            <div class="settings-group">
                                <h4>إعدادات المظهر</h4>
                                <div class="setting-item">
                                    <label class="setting-label">
                                        <input type="checkbox" id="darkModeToggle">
                                        <span class="setting-text">الوضع المظلم</span>
                                    </label>
                                </div>
                                <div class="setting-item">
                                    <label class="setting-label">
                                        <span class="setting-text">حجم الخط</span>
                                        <select id="fontSizeSelect">
                                            <option value="small">صغير</option>
                                            <option value="medium" selected>متوسط</option>
                                            <option value="large">كبير</option>
                                        </select>
                                    </label>
                                </div>
                            </div>

                            <div class="settings-group">
                                <h4>إعدادات الدردشة</h4>
                                <div class="setting-item">
                                    <label class="setting-label">
                                        <input type="checkbox" id="enterToSendToggle" checked>
                                        <span class="setting-text">إرسال بالضغط على Enter</span>
                                    </label>
                                </div>
                                <div class="setting-item">
                                    <label class="setting-label">
                                        <input type="checkbox" id="typingIndicatorToggle" checked>
                                        <span class="setting-text">إظهار مؤشر الكتابة</span>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- تبويب الخصوصية -->
                        <div class="tab-panel" id="privacy-tab">
                            <div class="settings-group">
                                <h4>إعدادات الخصوصية</h4>
                                <div class="setting-item">
                                    <label class="setting-label">
                                        <span class="setting-text">من يمكنه رؤية حالة الاتصال</span>
                                        <select id="onlineStatusPrivacy">
                                            <option value="everyone">الجميع</option>
                                            <option value="contacts">جهات الاتصال فقط</option>
                                            <option value="nobody">لا أحد</option>
                                        </select>
                                    </label>
                                </div>
                                <div class="setting-item">
                                    <label class="setting-label">
                                        <span class="setting-text">من يمكنه إرسال رسائل مباشرة</span>
                                        <select id="directMessagePrivacy">
                                            <option value="everyone">الجميع</option>
                                            <option value="contacts">جهات الاتصال فقط</option>
                                        </select>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- تبويب الإشعارات -->
                        <div class="tab-panel" id="notifications-tab">
                            <div class="settings-group">
                                <h4>إعدادات الإشعارات</h4>
                                <div class="setting-item">
                                    <label class="setting-label">
                                        <input type="checkbox" id="desktopNotificationsToggle" checked>
                                        <span class="setting-text">إشعارات سطح المكتب</span>
                                    </label>
                                </div>
                                <div class="setting-item">
                                    <label class="setting-label">
                                        <input type="checkbox" id="soundNotificationsToggle" checked>
                                        <span class="setting-text">الأصوات</span>
                                    </label>
                                </div>
                                <div class="setting-item">
                                    <label class="setting-label">
                                        <span class="setting-text">نغمة الإشعار</span>
                                        <select id="notificationSoundSelect">
                                            <option value="default">افتراضي</option>
                                            <option value="bell">جرس</option>
                                            <option value="chime">رنين</option>
                                            <option value="pop">نقرة</option>
                                            <option value="message">رسالة</option>
                                            <option value="mention">ذكر</option>
                                        </select>
                                    </label>
                                </div>

                                <div class="setting-item">
                                    <label class="setting-label">
                                        <span class="setting-text">مستوى الصوت</span>
                                        <input type="range" id="volumeSlider" min="0" max="1" step="0.1" value="0.7">
                                        <span id="volumeValue">70%</span>
                                    </label>
                                </div>

                                <div class="setting-item">
                                    <label class="setting-label">
                                        <input type="checkbox" id="doNotDisturbToggle">
                                        <span class="setting-text">وضع عدم الإزعاج</span>
                                    </label>
                                </div>

                                <div class="setting-item">
                                    <label class="setting-label">
                                        <input type="checkbox" id="quietHoursToggle">
                                        <span class="setting-text">ساعات الهدوء</span>
                                    </label>
                                </div>

                                <div class="setting-item quiet-hours-settings" style="display: none;">
                                    <div class="quiet-hours-config">
                                        <label>من الساعة:
                                            <input type="time" id="quietHoursStart" value="22:00">
                                        </label>
                                        <label>إلى الساعة:
                                            <input type="time" id="quietHoursEnd" value="08:00">
                                        </label>
                                    </div>
                                </div>

                                <div class="setting-item">
                                    <button class="btn btn-secondary" id="testNotificationBtn">
                                        <i class="fas fa-bell"></i>
                                        اختبار الإشعار
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- مؤشر حالة الاتصال -->
    <div id="connectionStatus" class="connection-status disconnected">
        <div class="connection-indicator"></div>
        <span id="connectionText">غير متصل</span>
    </div>

    <!-- رسائل التنبيه -->
    <div id="notifications" class="notifications-container"></div>

    <!-- لوحة الرموز التعبيرية -->
    <div id="emojiPanel" class="emoji-panel">
        <div class="emoji-header">
            <div class="emoji-tabs">
                <button class="emoji-tab active" data-category="recent">🕒</button>
                <button class="emoji-tab" data-category="smileys">😀</button>
                <button class="emoji-tab" data-category="people">👥</button>
                <button class="emoji-tab" data-category="nature">🌿</button>
                <button class="emoji-tab" data-category="food">🍕</button>
                <button class="emoji-tab" data-category="activities">⚽</button>
                <button class="emoji-tab" data-category="travel">✈️</button>
                <button class="emoji-tab" data-category="objects">💡</button>
                <button class="emoji-tab" data-category="symbols">❤️</button>
                <button class="emoji-tab" data-category="flags">🏳️</button>
            </div>
            <button class="emoji-close" id="closeEmojiPanel">×</button>
        </div>
        <div class="emoji-content" id="emojiContent">
            <!-- الرموز التعبيرية ستظهر هنا -->
        </div>
        <div class="emoji-search">
            <input type="text" id="emojiSearch" placeholder="البحث عن رمز تعبيري...">
        </div>
    </div>

    <!-- نافذة البروفايل -->
    <div id="profileModal" class="modal">
        <div class="modal-content profile-modal">
            <div class="modal-header">
                <h2><i class="fas fa-user"></i> البروفايل الشخصي</h2>
                <button class="modal-close" id="closeProfileModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div class="modal-body">
                <div class="profile-section">
                    <div class="profile-avatar-section">
                        <div class="profile-avatar" id="profileAvatar">
                            <img id="profileAvatarImg" src="" alt="الصورة الشخصية" style="display: none;">
                            <div id="profileAvatarText" class="avatar-text">U</div>
                        </div>
                        <button class="change-avatar-btn" id="changeAvatarBtn">
                            <i class="fas fa-camera"></i> تغيير الصورة
                        </button>
                        <input type="file" id="avatarInput" accept="image/*" style="display: none;">
                    </div>

                    <form id="profileForm" class="profile-form">
                        <div class="form-group">
                            <label for="profileUsername">اسم المستخدم</label>
                            <input type="text" id="profileUsername" name="username" readonly>
                        </div>

                        <div class="form-group">
                            <label for="profileEmail">البريد الإلكتروني</label>
                            <input type="email" id="profileEmail" name="email">
                        </div>

                        <div class="form-group">
                            <label for="profileBio">نبذة شخصية</label>
                            <textarea id="profileBio" name="bio" placeholder="اكتب نبذة عن نفسك..." rows="3"></textarea>
                        </div>

                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> حفظ التغييرات
                            </button>
                            <button type="button" class="btn btn-secondary" id="changePasswordBtn">
                                <i class="fas fa-key"></i> تغيير كلمة المرور
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script src="/static/js/emoji-picker.js"></script>
    <script src="/static/js/file-upload.js"></script>
    <script src="/static/js/notifications.js"></script>
    <script src="/static/js/performance.js"></script>
    <script src="/static/js/app.js"></script>
</body>
</html>
