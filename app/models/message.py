"""
Message model for the chat application
"""
from datetime import datetime
from enum import Enum
from sqlalchemy import Column, Integer, String, Text, DateTime, ForeignKey, Enum as S<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Boolean
from sqlalchemy.orm import relationship

from app.models.database import Base


class MessageStatus(str, Enum):
    """Message status enumeration"""
    SENT = "sent"
    DELIVERED = "delivered"
    READ = "read"


class MessageType(str, Enum):
    """Message type enumeration"""
    DIRECT = "direct"  # One-to-one message
    ROOM = "room"      # Room message


class MessageContentType(str, Enum):
    """Message content type enumeration"""
    TEXT = "text"           # Text message
    IMAGE = "image"         # Image file
    FILE = "file"           # Document/file
    AUDIO = "audio"         # Audio message
    VIDEO = "video"         # Video file
    VOICE = "voice"         # Voice note
    LOCATION = "location"   # Location sharing
    CONTACT = "contact"     # Contact sharing


class Message(Base):
    """Message model"""
    __tablename__ = "messages"
    
    id = Column(Integer, primary_key=True, index=True)
    content = Column(Text, nullable=False)
    message_type = Column(SQLEnum(MessageType), nullable=False, default=MessageType.DIRECT)
    content_type = Column(SQLEnum(MessageContentType), nullable=False, default=MessageContentType.TEXT)
    status = Column(SQLEnum(MessageStatus), nullable=False, default=MessageStatus.SENT)

    # File/Media information
    file_url = Column(String(500), nullable=True)  # URL for uploaded files
    file_name = Column(String(255), nullable=True)  # Original file name
    file_size = Column(Integer, nullable=True)  # File size in bytes
    file_type = Column(String(100), nullable=True)  # MIME type
    thumbnail_url = Column(String(500), nullable=True)  # Thumbnail for images/videos

    # Message metadata
    message_metadata = Column(JSON, nullable=True)  # Additional metadata (duration for audio, dimensions for images, etc.)
    is_edited = Column(Boolean, default=False)  # Whether message was edited
    is_forwarded = Column(Boolean, default=False)  # Whether message was forwarded
    reply_to_id = Column(Integer, ForeignKey("messages.id"), nullable=True)  # Reply to another message

    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Foreign keys
    sender_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    recipient_id = Column(Integer, ForeignKey("users.id"), nullable=True)  # Null for room messages
    room_id = Column(Integer, ForeignKey("rooms.id"), nullable=True)  # Null for direct messages
    
    # Relationships
    sender = relationship("User", foreign_keys=[sender_id], back_populates="sent_messages")
    recipient = relationship("User", foreign_keys=[recipient_id], back_populates="received_messages")
    room = relationship("Room", back_populates="messages")
    reply_to = relationship("Message", remote_side=[id], backref="replies")
    
    def __repr__(self):
        return f"<Message(id={self.id}, sender_id={self.sender_id}, type='{self.message_type}')>"
    
    def to_dict(self):
        """Convert message to dictionary"""
        return {
            "id": self.id,
            "content": self.content,
            "message_type": self.message_type.value,
            "content_type": self.content_type.value,
            "status": self.status.value,
            "sender_id": self.sender_id,
            "recipient_id": self.recipient_id,
            "room_id": self.room_id,
            "file_url": self.file_url,
            "file_name": self.file_name,
            "file_size": self.file_size,
            "file_type": self.file_type,
            "thumbnail_url": self.thumbnail_url,
            "metadata": self.message_metadata,
            "is_edited": self.is_edited,
            "is_forwarded": self.is_forwarded,
            "reply_to_id": self.reply_to_id,
            "reply_to": self.reply_to.to_dict() if self.reply_to else None,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }
