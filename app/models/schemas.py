"""
Pydantic schemas for request/response validation
"""
from datetime import datetime
from typing import Optional, List, List
from pydantic import BaseModel, EmailStr, Field

from app.models.message import MessageStatus, MessageType
from app.models.room import RoomType, MemberRole


# User schemas
class UserBase(BaseModel):
    username: str = Field(..., min_length=3, max_length=50)
    email: EmailStr


class UserCreate(UserBase):
    password: str = Field(..., min_length=6, max_length=100)
    phone: Optional[int]  = None


class UserUpdate(BaseModel):
    username: Optional[str] = Field(None, min_length=3, max_length=50)
    email: Optional[EmailStr] = None
    avatar_url: Optional[str] = None
    


class UserResponse(UserBase):
    id: int
    avatar_url: Optional[str] = None
    is_online: bool
    is_active: bool
    created_at: datetime
    last_seen: Optional[datetime] = None

    class Config:
        from_attributes = True


class UserLogin(BaseModel):
    username: str
    password: str


# Token schemas
class Token(BaseModel):
    access_token: str
    token_type: str = "bearer"


class TokenData(BaseModel):
    username: Optional[str] = None


# Message schemas
class MessageBase(BaseModel):
    content: str = Field(..., min_length=1, max_length=5000)


class MessageCreate(MessageBase):
    recipient_id: Optional[int] = None
    room_id: Optional[int] = None
    message_type: MessageType = MessageType.DIRECT


class MessageUpdate(BaseModel):
    content: Optional[str] = Field(None, min_length=1, max_length=5000)
    status: Optional[MessageStatus] = None


class MessageResponse(MessageBase):
    id: int
    message_type: MessageType
    status: MessageStatus
    sender_id: int
    recipient_id: Optional[int] = None
    room_id: Optional[int] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


# Room schemas
class RoomBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=100)
    description: Optional[str] = Field(None, max_length=500)
    room_type: RoomType = RoomType.PUBLIC


class RoomCreate(RoomBase):
    member_ids: Optional[List[int]] = Field(default_factory=list, description="قائمة معرفات الأعضاء لإضافتهم للمجموعة")


class RoomUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=100)
    description: Optional[str] = Field(None, max_length=500)
    room_type: Optional[RoomType] = None


class RoomResponse(RoomBase):
    id: int
    created_by: int
    is_active: bool
    created_at: datetime
    member_count: int = 0

    class Config:
        from_attributes = True


# Room Member schemas
class RoomMemberCreate(BaseModel):
    user_id: int
    room_id: int
    role: MemberRole = MemberRole.MEMBER


class RoomMemberUpdate(BaseModel):
    role: Optional[MemberRole] = None
    is_active: Optional[bool] = None


class RoomMemberResponse(BaseModel):
    id: int
    user_id: int
    room_id: int
    role: MemberRole
    joined_at: datetime
    is_active: bool

    class Config:
        from_attributes = True


# WebSocket schemas
class WebSocketMessage(BaseModel):
    type: str  # message, join_room, leave_room, user_status, etc.
    data: dict


class ChatMessage(BaseModel):
    content: str
    recipient_id: Optional[int] = None
    room_id: Optional[int] = None
    message_type: MessageType = MessageType.DIRECT


# Pagination schemas
class PaginationParams(BaseModel):
    page: int = Field(1, ge=1)
    size: int = Field(50, ge=1, le=100)


class PaginatedResponse(BaseModel):
    items: List[dict]
    total: int
    page: int
    size: int
    pages: int
