"""
Messages API endpoints
"""
from fastapi import APIRouter, Depends, Query, Path, UploadFile, File, Form
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
import os
import uuid
from pathlib import Path as PathLib

from app.models.database import get_async_session
from app.models.schemas import (
    MessageCreate, MessageResponse, MessageUpdate, 
    PaginationParams, PaginatedResponse
)
from app.models.message import MessageStatus
from app.services.message_service import MessageService
from app.auth import get_current_active_user
from app.models.user import User

router = APIRouter(prefix="/messages", tags=["Messages"])


@router.get("/conversations")
async def get_conversations(
    db: AsyncSession = Depends(get_async_session),
    current_user: User = Depends(get_current_active_user)
):
    """Get user's conversations"""
    conversations = await MessageService.get_user_conversations(current_user.id, db)
    return conversations


@router.delete("/conversation/{user_id}")
async def delete_conversation(
    user_id: int = Path(..., description="ID of the other user"),
    db: AsyncSession = Depends(get_async_session),
    current_user: User = Depends(get_current_active_user)
):
    """Delete all messages in a conversation"""
    try:
        await MessageService.delete_conversation(current_user.id, user_id, db)
        return {"message": "Conversation deleted successfully"}
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/", response_model=MessageResponse, status_code=201)
async def create_message(
    message_data: MessageCreate,
    db: AsyncSession = Depends(get_async_session),
    current_user: User = Depends(get_current_active_user)
):
    """Create a new message"""
    message = await MessageService.create_message(message_data, current_user, db)
    return MessageResponse.from_orm(message)


@router.get("/conversation/{user_id}")
async def get_conversation_messages(
    user_id: int = Path(..., description="ID of the other user"),
    page: int = Query(1, ge=1),
    size: int = Query(50, ge=1, le=100),
    db: AsyncSession = Depends(get_async_session),
    current_user: User = Depends(get_current_active_user)
):
    """Get messages in a conversation with another user"""
    pagination = PaginationParams(page=page, size=size)
    messages, total = await MessageService.get_direct_messages(
        current_user.id, user_id, pagination, db
    )

    # Convert messages to simple format for frontend
    message_items = []
    for message in messages:
        # Get sender name safely
        sender_name = 'Unknown'
        if message.sender_id == current_user.id:
            sender_name = current_user.username
        else:
            # Get the other user's name
            sender_stmt = select(User).where(User.id == message.sender_id)
            sender_result = await db.execute(sender_stmt)
            sender_user = sender_result.scalar_one_or_none()
            if sender_user:
                sender_name = sender_user.username

        message_items.append({
            'id': message.id,
            'content': message.content,
            'sender_id': message.sender_id,
            'sender_name': sender_name,
            'recipient_id': message.recipient_id,
            'created_at': message.created_at.isoformat(),
            'status': message.status.value
        })

    return {
        "messages": message_items,
        "total": total,
        "page": page,
        "size": size,
        "has_more": len(message_items) == size
    }


@router.post("/upload")
async def upload_file(
    file: UploadFile = File(...),
    recipient_id: int = Form(...),
    db: AsyncSession = Depends(get_async_session),
    current_user: User = Depends(get_current_active_user)
):
    """Upload and send a file"""
    try:
        # إنشاء مجلد الرفع إذا لم يكن موجوداً
        upload_dir = PathLib("uploads")
        upload_dir.mkdir(exist_ok=True)

        # إنشاء اسم ملف فريد
        file_extension = PathLib(file.filename).suffix
        unique_filename = f"{uuid.uuid4()}{file_extension}"
        file_path = upload_dir / unique_filename

        # حفظ الملف
        with open(file_path, "wb") as buffer:
            content = await file.read()
            buffer.write(content)

        # إنشاء رسالة مع معلومات الملف
        file_info = {
            "type": "file",
            "filename": file.filename,
            "size": len(content),
            "url": f"/uploads/{unique_filename}",
            "mime_type": file.content_type
        }

        message_data = MessageCreate(
            content=f"📎 {file.filename}",
            recipient_id=recipient_id,
            file_info=file_info
        )

        message = await MessageService.create_message(message_data, current_user, db)

        return {
            "message": "File uploaded successfully",
            "file_url": file_info["url"],
            "message_id": message.id
        }

    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.put("/{message_id}")
async def update_message(
    message_id: int = Path(..., description="ID of the message to update"),
    message_update: MessageUpdate = ...,
    db: AsyncSession = Depends(get_async_session),
    current_user: User = Depends(get_current_active_user)
):
    """Update a message (only sender can update)"""
    try:
        updated_message = await MessageService.update_message(
            message_id, message_update, current_user.id, db
        )
        return {
            'id': updated_message.id,
            'content': updated_message.content,
            'sender_id': updated_message.sender_id,
            'sender_name': current_user.username,
            'recipient_id': updated_message.recipient_id,
            'created_at': updated_message.created_at.isoformat(),
            'updated_at': updated_message.updated_at.isoformat(),
            'status': updated_message.status.value,
            'is_edited': True
        }
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.delete("/{message_id}")
async def delete_message(
    message_id: int = Path(..., description="ID of the message to delete"),
    db: AsyncSession = Depends(get_async_session),
    current_user: User = Depends(get_current_active_user)
):
    """Delete a message (only sender can delete)"""
    try:
        await MessageService.delete_message(message_id, current_user.id, db)
        return {"message": "Message deleted successfully"}
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.get("/direct/{user_id}", response_model=PaginatedResponse)
async def get_direct_messages(
    user_id: int = Path(..., description="ID of the other user"),
    page: int = Query(1, ge=1),
    size: int = Query(50, ge=1, le=100),
    db: AsyncSession = Depends(get_async_session),
    current_user: User = Depends(get_current_active_user)
):
    """Get direct messages between current user and another user"""
    pagination = PaginationParams(page=page, size=size)
    messages, total = await MessageService.get_direct_messages(
        current_user.id, user_id, pagination, db
    )

    # Convert messages to response format
    message_items = [MessageResponse.from_orm(message).dict() for message in messages]

    pages = (total + size - 1) // size

    return PaginatedResponse(
        items=message_items,
        total=total,
        page=page,
        size=size,
        pages=pages
    )


@router.get("/room/{room_id}", response_model=PaginatedResponse)
async def get_room_messages(
    room_id: int = Path(..., description="ID of the room"),
    page: int = Query(1, ge=1),
    size: int = Query(50, ge=1, le=100),
    db: AsyncSession = Depends(get_async_session),
    current_user: User = Depends(get_current_active_user)
):
    """Get messages from a room"""
    pagination = PaginationParams(page=page, size=size)
    messages, total = await MessageService.get_room_messages(
        room_id, pagination, current_user, db
    )
    
    # Convert messages to response format
    message_items = [MessageResponse.from_orm(message).dict() for message in messages]
    
    pages = (total + size - 1) // size
    
    return PaginatedResponse(
        items=message_items,
        total=total,
        page=page,
        size=size,
        pages=pages
    )


@router.patch("/{message_id}/status", response_model=MessageResponse)
async def update_message_status(
    message_id: int = Path(..., description="ID of the message"),
    status: MessageStatus = Query(..., description="New message status"),
    db: AsyncSession = Depends(get_async_session),
    current_user: User = Depends(get_current_active_user)
):
    """Update message status (delivered, read)"""
    message = await MessageService.update_message_status(
        message_id, status, current_user, db
    )
    return MessageResponse.from_orm(message)


@router.get("/search")
async def search_messages(
    q: str = Query(..., description="Search query"),
    chat_id: int = Query(..., description="Chat/User ID to search in"),
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    db: AsyncSession = Depends(get_async_session),
    current_user: User = Depends(get_current_active_user)
):
    """Search messages in a conversation"""
    try:
        results = await MessageService.search_messages(
            current_user.id, chat_id, q, page, size, db
        )

        # Convert to simple format
        search_results = []
        for message in results:
            search_results.append({
                'id': message.id,
                'content': message.content,
                'sender_id': message.sender_id,
                'sender_name': message.sender.username if message.sender else 'Unknown',
                'created_at': message.created_at.isoformat(),
                'content_type': message.content_type.value if message.content_type else 'text'
            })

        return search_results

    except Exception as e:
        from fastapi import HTTPException, status
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Search failed: {str(e)}"
        )
