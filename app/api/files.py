"""
File upload and management endpoints
"""
import os
import uuid
from typing import List
from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, status
from fastapi.responses import FileResponse
from sqlalchemy.ext.asyncio import AsyncSession
import aiofiles
from PIL import Image
import io

from app.models.user import User
from app.models.database import get_async_session
from app.auth import get_current_active_user
# from app.core.config import settings  # غير مطلوب حالياً

router = APIRouter(prefix="/files", tags=["files"])

# إعدادات الملفات
UPLOAD_DIR = "uploads"
ALLOWED_EXTENSIONS = {
    'image': ['.jpg', '.jpeg', '.png', '.gif', '.webp'],
    'video': ['.mp4', '.webm', '.ogg', '.avi', '.mov'],
    'audio': ['.mp3', '.wav', '.ogg', '.m4a', '.aac'],
    'document': ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.txt']
}
MAX_FILE_SIZE = 50 * 1024 * 1024  # 50MB

# إنشاء مجلدات الرفع
os.makedirs(UPLOAD_DIR, exist_ok=True)
for file_type in ALLOWED_EXTENSIONS.keys():
    os.makedirs(os.path.join(UPLOAD_DIR, file_type), exist_ok=True)
    os.makedirs(os.path.join(UPLOAD_DIR, file_type, 'thumbnails'), exist_ok=True)


def get_file_type(filename: str) -> str:
    """تحديد نوع الملف من الامتداد"""
    ext = os.path.splitext(filename)[1].lower()
    for file_type, extensions in ALLOWED_EXTENSIONS.items():
        if ext in extensions:
            return file_type
    return 'document'


def generate_filename(original_filename: str) -> str:
    """إنشاء اسم ملف فريد"""
    ext = os.path.splitext(original_filename)[1]
    return f"{uuid.uuid4()}{ext}"


async def create_thumbnail(file_path: str, thumbnail_path: str, size: tuple = (300, 300)):
    """إنشاء صورة مصغرة للصور"""
    try:
        with Image.open(file_path) as img:
            # تحويل إلى RGB إذا كانت الصورة RGBA
            if img.mode in ('RGBA', 'LA', 'P'):
                img = img.convert('RGB')
            
            # إنشاء صورة مصغرة مع الحفاظ على النسبة
            img.thumbnail(size, Image.Resampling.LANCZOS)
            img.save(thumbnail_path, 'JPEG', quality=85, optimize=True)
            return True
    except Exception as e:
        print(f"Error creating thumbnail: {e}")
        return False


@router.post("/upload")
async def upload_file(
    file: UploadFile = File(...),
    current_user: User = Depends(get_current_active_user)
):
    """رفع ملف"""
    
    # التحقق من حجم الملف
    if file.size > MAX_FILE_SIZE:
        raise HTTPException(
            status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
            detail=f"File too large. Maximum size is {MAX_FILE_SIZE // (1024*1024)}MB"
        )
    
    # التحقق من نوع الملف
    file_type = get_file_type(file.filename)
    ext = os.path.splitext(file.filename)[1].lower()
    
    if ext not in sum(ALLOWED_EXTENSIONS.values(), []):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="File type not allowed"
        )
    
    try:
        # إنشاء اسم ملف فريد
        unique_filename = generate_filename(file.filename)
        file_path = os.path.join(UPLOAD_DIR, file_type, unique_filename)
        
        # حفظ الملف
        async with aiofiles.open(file_path, 'wb') as f:
            content = await file.read()
            await f.write(content)
        
        # إنشاء صورة مصغرة للصور
        thumbnail_url = None
        if file_type == 'image':
            thumbnail_filename = f"thumb_{unique_filename}"
            thumbnail_path = os.path.join(UPLOAD_DIR, file_type, 'thumbnails', thumbnail_filename)
            
            if await create_thumbnail(file_path, thumbnail_path):
                thumbnail_url = f"/api/v1/files/{file_type}/thumbnails/{thumbnail_filename}"
        
        # إنشاء URL للملف
        file_url = f"/api/v1/files/{file_type}/{unique_filename}"
        
        return {
            "file_id": unique_filename,
            "file_url": file_url,
            "file_name": file.filename,
            "file_type": file.content_type,
            "file_size": file.size,
            "content_type": file_type,
            "thumbnail_url": thumbnail_url,
            "uploaded_by": current_user.id
        }
        
    except Exception as e:
        # حذف الملف في حالة الخطأ
        if os.path.exists(file_path):
            os.remove(file_path)
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to upload file: {str(e)}"
        )


@router.get("/{file_type}/{filename}")
async def get_file(file_type: str, filename: str):
    """الحصول على ملف"""
    
    if file_type not in ALLOWED_EXTENSIONS:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid file type"
        )
    
    file_path = os.path.join(UPLOAD_DIR, file_type, filename)
    
    if not os.path.exists(file_path):
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="File not found"
        )
    
    return FileResponse(
        file_path,
        filename=filename,
        media_type='application/octet-stream'
    )


@router.get("/{file_type}/thumbnails/{filename}")
async def get_thumbnail(file_type: str, filename: str):
    """الحصول على صورة مصغرة"""
    
    if file_type not in ALLOWED_EXTENSIONS:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid file type"
        )
    
    thumbnail_path = os.path.join(UPLOAD_DIR, file_type, 'thumbnails', filename)
    
    if not os.path.exists(thumbnail_path):
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Thumbnail not found"
        )
    
    return FileResponse(
        thumbnail_path,
        media_type='image/jpeg'
    )


@router.delete("/{file_type}/{filename}")
async def delete_file(
    file_type: str,
    filename: str,
    current_user: User = Depends(get_current_active_user)
):
    """حذف ملف"""
    
    if file_type not in ALLOWED_EXTENSIONS:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid file type"
        )
    
    file_path = os.path.join(UPLOAD_DIR, file_type, filename)
    thumbnail_path = os.path.join(UPLOAD_DIR, file_type, 'thumbnails', f"thumb_{filename}")
    
    try:
        # حذف الملف الأصلي
        if os.path.exists(file_path):
            os.remove(file_path)
        
        # حذف الصورة المصغرة إن وجدت
        if os.path.exists(thumbnail_path):
            os.remove(thumbnail_path)
        
        return {"message": "File deleted successfully"}
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete file: {str(e)}"
        )


@router.get("/info/{file_type}/{filename}")
async def get_file_info(
    file_type: str,
    filename: str,
    current_user: User = Depends(get_current_active_user)
):
    """الحصول على معلومات الملف"""
    
    if file_type not in ALLOWED_EXTENSIONS:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid file type"
        )
    
    file_path = os.path.join(UPLOAD_DIR, file_type, filename)
    
    if not os.path.exists(file_path):
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="File not found"
        )
    
    file_stats = os.stat(file_path)
    
    return {
        "filename": filename,
        "file_type": file_type,
        "size": file_stats.st_size,
        "created_at": file_stats.st_ctime,
        "modified_at": file_stats.st_mtime,
        "file_url": f"/api/v1/files/{file_type}/{filename}"
    }
