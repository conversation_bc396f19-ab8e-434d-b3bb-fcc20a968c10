"""
Message service
"""
from typing import List, Optional, Dict, Any
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, or_, and_, desc, case
from fastapi import HTTPException, status

from app.models.message import Message, MessageType, MessageStatus
from app.models.user import User
from app.models.room import Room, RoomMember
from app.models.schemas import MessageCreate, MessageUpdate, PaginationParams
from app.utils.logger import log_message_action, log_error


class MessageService:
    """Message service class"""
    
    @staticmethod
    async def create_message(
        message_data: MessageCreate,
        sender: User,
        db: AsyncSession
    ) -> Message:
        """Create a new message"""
        try:
            # Validate message type and recipients
            if message_data.message_type == MessageType.DIRECT:
                if not message_data.recipient_id:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail="Recipient ID is required for direct messages"
                    )
                
                # Check if recipient exists
                stmt = select(User).where(User.id == message_data.recipient_id)
                result = await db.execute(stmt)
                recipient = result.scalar_one_or_none()
                if not recipient:
                    raise HTTPException(
                        status_code=status.HTTP_404_NOT_FOUND,
                        detail="Recipient not found"
                    )
                
                # Check if sender is not sending to themselves
                if sender.id == message_data.recipient_id:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail="Cannot send message to yourself"
                    )
                
            elif message_data.message_type == MessageType.ROOM:
                if not message_data.room_id:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail="Room ID is required for room messages"
                    )
                
                # Check if room exists and user is a member
                stmt = select(RoomMember).where(
                    and_(
                        RoomMember.room_id == message_data.room_id,
                        RoomMember.user_id == sender.id,
                        RoomMember.is_active == True
                    )
                )
                result = await db.execute(stmt)
                membership = result.scalar_one_or_none()
                if not membership:
                    raise HTTPException(
                        status_code=status.HTTP_403_FORBIDDEN,
                        detail="You are not a member of this room"
                    )
            
            # Create message
            message = Message(
                content=message_data.content,
                message_type=message_data.message_type,
                sender_id=sender.id,
                recipient_id=message_data.recipient_id,
                room_id=message_data.room_id
            )
            
            db.add(message)
            await db.commit()
            await db.refresh(message)
            
            # Log message creation
            log_message_action(
                "MessageSent",
                message_id=f"msg_{message.id}",
                sender_id=sender.id,
                recipient_id=message_data.recipient_id,
                room_id=message_data.room_id
            )
            
            return message
            
        except HTTPException:
            raise
        except Exception as e:
            log_error("Error creating message", e, user_id=sender.id)
            await db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create message"
            )
    
    @staticmethod
    async def get_direct_messages(
        user1_id: int,
        user2_id: int,
        pagination: PaginationParams,
        db: AsyncSession
    ) -> tuple[List[Message], int]:
        """Get direct messages between two users"""
        try:
            # Base query for messages between two users
            base_filter = and_(
                Message.message_type == MessageType.DIRECT,
                or_(
                    and_(Message.sender_id == user1_id, Message.recipient_id == user2_id),
                    and_(Message.sender_id == user2_id, Message.recipient_id == user1_id)
                )
            )
            
            # Count query
            count_stmt = select(func.count(Message.id)).where(base_filter)
            count_result = await db.execute(count_stmt)
            total = count_result.scalar()
            
            # Messages query with pagination
            offset = (pagination.page - 1) * pagination.size
            stmt = select(Message).where(base_filter)
            stmt = stmt.order_by(Message.created_at.desc())
            stmt = stmt.offset(offset).limit(pagination.size)
            
            result = await db.execute(stmt)
            messages = list(result.scalars().all())
            
            return messages, total
            
        except Exception as e:
            log_error("Error getting direct messages", e, user_id=user1_id)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to get messages"
            )
    
    @staticmethod
    async def get_room_messages(
        room_id: int,
        pagination: PaginationParams,
        user: User,
        db: AsyncSession
    ) -> tuple[List[Message], int]:
        """Get messages from a room"""
        try:
            # Check if user is a member of the room
            stmt = select(RoomMember).where(
                and_(
                    RoomMember.room_id == room_id,
                    RoomMember.user_id == user.id,
                    RoomMember.is_active == True
                )
            )
            result = await db.execute(stmt)
            membership = result.scalar_one_or_none()
            if not membership:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="You are not a member of this room"
                )
            
            # Base query for room messages
            base_filter = and_(
                Message.message_type == MessageType.ROOM,
                Message.room_id == room_id
            )
            
            # Count query
            count_stmt = select(func.count(Message.id)).where(base_filter)
            count_result = await db.execute(count_stmt)
            total = count_result.scalar()
            
            # Messages query with pagination
            offset = (pagination.page - 1) * pagination.size
            stmt = select(Message).where(base_filter)
            stmt = stmt.order_by(Message.created_at.desc())
            stmt = stmt.offset(offset).limit(pagination.size)
            
            result = await db.execute(stmt)
            messages = list(result.scalars().all())
            
            return messages, total
            
        except HTTPException:
            raise
        except Exception as e:
            log_error("Error getting room messages", e, room_id=room_id, user_id=user.id)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to get room messages"
            )
    
    @staticmethod
    async def update_message_status(
        message_id: int,
        new_status: MessageStatus,
        user: User,
        db: AsyncSession
    ) -> Message:
        """Update message status (delivered, read)"""
        try:
            # Get message
            stmt = select(Message).where(Message.id == message_id)
            result = await db.execute(stmt)
            message = result.scalar_one_or_none()
            
            if not message:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Message not found"
                )
            
            # Check if user is the recipient (for direct messages) or member (for room messages)
            if message.message_type == MessageType.DIRECT:
                if message.recipient_id != user.id:
                    raise HTTPException(
                        status_code=status.HTTP_403_FORBIDDEN,
                        detail="You can only update status of messages sent to you"
                    )
            elif message.message_type == MessageType.ROOM:
                # Check room membership
                stmt = select(RoomMember).where(
                    and_(
                        RoomMember.room_id == message.room_id,
                        RoomMember.user_id == user.id,
                        RoomMember.is_active == True
                    )
                )
                result = await db.execute(stmt)
                membership = result.scalar_one_or_none()
                if not membership:
                    raise HTTPException(
                        status_code=status.HTTP_403_FORBIDDEN,
                        detail="You are not a member of this room"
                    )
            
            # Update status
            message.status = new_status
            await db.commit()
            await db.refresh(message)
            
            log_message_action(
                f"MessageStatus{new_status.value.title()}",
                message_id=f"msg_{message.id}",
                sender_id=message.sender_id,
                recipient_id=message.recipient_id,
                room_id=message.room_id
            )
            
            return message
            
        except HTTPException:
            raise
        except Exception as e:
            log_error("Error updating message status", e, message_id=message_id, user_id=user.id)
            await db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to update message status"
            )

    @staticmethod
    async def get_user_conversations(user_id: int, db: AsyncSession) -> List[Dict[str, Any]]:
        """Get user's conversations with last message info - WhatsApp style"""
        try:
            conversations = []

            # Simple query to get all messages for this user
            messages_stmt = select(Message).where(
                or_(Message.sender_id == user_id, Message.recipient_id == user_id)
            ).order_by(desc(Message.created_at))

            result = await db.execute(messages_stmt)
            all_messages = result.scalars().all()

            # Track users we've already processed
            seen_users = set()

            for message in all_messages:
                # Find the other user in this conversation
                other_user_id = None
                if message.sender_id == user_id:
                    other_user_id = message.recipient_id
                elif message.recipient_id == user_id:
                    other_user_id = message.sender_id

                # Skip if we've already processed this user or if it's a group message
                if not other_user_id or other_user_id in seen_users:
                    continue

                seen_users.add(other_user_id)

                # Get user info
                user_stmt = select(User).where(User.id == other_user_id)
                user_result = await db.execute(user_stmt)
                other_user = user_result.scalar_one_or_none()

                if other_user:
                    conversation = {
                        'id': other_user_id,
                        'name': other_user.username,
                        'avatar_url': getattr(other_user, 'avatar_url', None),
                        'is_online': getattr(other_user, 'is_online', True),
                        'is_group': False,
                        'last_message': {
                            'content': message.content,
                            'created_at': message.created_at.isoformat(),
                            'sender_id': message.sender_id
                        },
                        'unread_count': 0
                    }
                    conversations.append(conversation)

            # Sort conversations by last message time
            conversations.sort(key=lambda x: x['last_message']['created_at'], reverse=True)

            return conversations

        except Exception as e:
            log_error("Error getting user conversations", e, user_id=user_id)
            return []

    @staticmethod
    async def update_message(
        message_id: int,
        message_update: 'MessageUpdate',
        user_id: int,
        db: AsyncSession
    ) -> Message:
        """Update a message (only sender can update)"""
        try:
            # Get the message
            stmt = select(Message).where(Message.id == message_id)
            result = await db.execute(stmt)
            message = result.scalar_one_or_none()

            if not message:
                raise ValueError("Message not found")

            if message.sender_id != user_id:
                raise ValueError("You can only edit your own messages")

            # Update the message
            if message_update.content:
                message.content = message_update.content

            message.updated_at = datetime.utcnow()

            await db.commit()
            await db.refresh(message)

            log_message_action("MessageUpdated", message_id=message_id, user_id=user_id)
            return message

        except Exception as e:
            await db.rollback()
            log_error("Error updating message", e, message_id=message_id, user_id=user_id)
            raise e

    @staticmethod
    async def delete_message(
        message_id: int,
        user_id: int,
        db: AsyncSession
    ) -> bool:
        """Delete a message (only sender can delete)"""
        try:
            # Get the message
            stmt = select(Message).where(Message.id == message_id)
            result = await db.execute(stmt)
            message = result.scalar_one_or_none()

            if not message:
                raise ValueError("Message not found")

            if message.sender_id != user_id:
                raise ValueError("You can only delete your own messages")

            # Delete the message
            await db.delete(message)
            await db.commit()

            log_message_action("MessageDeleted", message_id=message_id, user_id=user_id)
            return True

        except Exception as e:
            await db.rollback()
            log_error("Error deleting message", e, message_id=message_id, user_id=user_id)
            raise e

    @staticmethod
    async def delete_conversation(
        user1_id: int,
        user2_id: int,
        db: AsyncSession
    ) -> bool:
        """Delete all messages in a conversation between two users"""
        try:
            # Delete all messages between the two users
            stmt = select(Message).where(
                and_(
                    Message.message_type == MessageType.DIRECT,
                    or_(
                        and_(Message.sender_id == user1_id, Message.recipient_id == user2_id),
                        and_(Message.sender_id == user2_id, Message.recipient_id == user1_id)
                    )
                )
            )

            result = await db.execute(stmt)
            messages = result.scalars().all()

            for message in messages:
                await db.delete(message)

            await db.commit()

            log_message_action("ConversationDeleted", user_id=user1_id, other_user_id=user2_id)
            return True

        except Exception as e:
            await db.rollback()
            log_error("Error deleting conversation", e, user1_id=user1_id, user2_id=user2_id)
            raise e

    @staticmethod
    async def search_messages(
        user_id: int,
        chat_id: int,
        search_query: str,
        page: int = 1,
        size: int = 20,
        db: AsyncSession = None
    ) -> List[Message]:
        """البحث في الرسائل"""
        try:
            from sqlalchemy.orm import selectinload

            # البحث في الرسائل المباشرة
            stmt = select(Message).options(
                selectinload(Message.sender),
                selectinload(Message.recipient)
            ).where(
                and_(
                    or_(
                        and_(Message.sender_id == user_id, Message.recipient_id == chat_id),
                        and_(Message.sender_id == chat_id, Message.recipient_id == user_id)
                    ),
                    Message.content.ilike(f"%{search_query}%")
                )
            ).order_by(desc(Message.created_at)).limit(size).offset((page - 1) * size)

            result = await db.execute(stmt)
            messages = result.scalars().all()

            log_message_action("MessagesSearched", user_id=user_id, chat_id=chat_id, query=search_query)

            return messages

        except Exception as e:
            log_error("Error searching messages", e, user_id=user_id, chat_id=chat_id)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to search messages: {str(e)}"
            )
