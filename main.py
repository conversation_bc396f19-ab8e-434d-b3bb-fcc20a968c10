"""
Main application entry point for the Chat Application
"""
import logging
import os
from datetime import datetime
from contextlib import asynccontextmanager

from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse

from config import settings
from app.utils.logger import setup_logging
from app.models.database import create_tables
from app.api.router import api_router
from app.sockets.websocket_endpoint import websocket_endpoint


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan events"""
    # Startup
    setup_logging()
    logging.info("Starting Chat Application...")
    
    # Create database tables
    await create_tables()
    logging.info("Database tables created/verified")
    
    yield
    
    # Shutdown
    logging.info("Shutting down Chat Application...")


# Create FastAPI application
app = FastAPI(
    title=settings.app_name,
    version=settings.app_version,
    debug=settings.debug,
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, specify actual origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include API router
app.include_router(api_router)

# WebSocket endpoint
app.websocket("/ws/{token}")(websocket_endpoint)

# Mount static files
app.mount("/static", StaticFiles(directory="frontend"), name="static")


@app.get("/")
async def root():
    """Serve the main chat application"""
    return FileResponse("frontend/index.html")


@app.get("/network-info")
async def network_info():
    """Serve network information page"""
    return FileResponse("frontend/network-info.html")


@app.get("/simple-login.html")
async def simple_login():
    """Serve simple login page"""
    return FileResponse("frontend/simple-login.html")


@app.get("/test-login.html")
async def test_login():
    """Serve test login page"""
    return FileResponse("frontend/test-login.html")


@app.get("/debug-login.html")
async def debug_login():
    """Serve debug login page"""
    return FileResponse("frontend/debug-login.html")


@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "version": "1.0.0",
        "services": {
            "database": "connected",
            "websocket": "available"
        }
    }

@app.get("/api")
async def api_root():
    """API root endpoint"""
    return {
        "message": "Welcome to Chat Application API",
        "version": settings.app_version,
        "status": "running"
    }


@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy"}


if __name__ == "__main__":
    import uvicorn
    
    # Ensure logs directory exists
    os.makedirs("logs", exist_ok=True)
    
    uvicorn.run(
        "main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug,
        log_level=settings.log_level.lower()
    )
